image: harbor-registry.eurodata.de/docker_hub/maven:3.8.7-eclipse-temurin-19


variables:
  MAVEN_CLI_OPTS: "-s ci_settings.xml -Dsettings.security=security-settings.xml --batch-mode"
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

cache:
  paths:
    - .m2/repository/
    - target/

stages:
  - test

appium:
  stage: test
  tags:
    - shared3
  script:
    #- 'openssl s_client -connect mobilecenter.eurodata.de:8443'
    - mvn $MAVEN_CLI_OPTS test
  allow_failure: true
  artifacts:
    when: always
    paths:
      - recordings
      - target/surefire-reports/*.xml
    reports:
      junit: target/surefire-reports/*.xml
