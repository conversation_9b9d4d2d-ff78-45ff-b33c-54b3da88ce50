package loginPage;

import org.junit.jupiter.api.Test;
import pages.webPages.LandingPage;
import pages.webPages.LoginPage;
import utils.BaseTest;
import xray.Xray;

/**
 * This test case should verify that users can not login if they enter only the password, click the "Sign in" button,
 * and leave the username field empty.
 *
 * <AUTHOR>
 */
public class T005_unsuccessfulLoginWithEmptyUsernameTest extends BaseTest {

    @Test
    @Xray(key = "XCAPITAIN-1543")
    public void unsuccessfulLoginWithEmptyUsername() {
        LandingPage landingPage = new LandingPage();
        LoginPage loginPage = landingPage.login();

        loginPage.setPassword("Tester-123")
                .signInAndStay()
                .verifyInvalidCredentialsErrorMessage();
    }
}
