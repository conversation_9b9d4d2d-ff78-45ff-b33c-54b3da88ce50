package loginPage;

import org.junit.jupiter.api.Test;
import pages.webPages.LandingPage;
import pages.webPages.LoginPage;
import utils.BaseTest;
import xray.Xray;

/**
 * This test case should verify that users can not login if they enter an invalid username, click the "Sign in" button,
 * and leave the password field empty.
 *
 * <AUTHOR>
 */
public class T004_unsuccessfulLoginWithInvalidUsernameAndEmptyPasswordTest extends BaseTest {

    @Test
    @Xray(key = "XCAPITAIN-1543")
    public void unsuccessfulLoginWithInvalidUsernameAndEmptyPassword() {
        LandingPage landingPage = new LandingPage();
        LoginPage loginPage = landingPage.login();

        loginPage.setUsername("<EMAIL>")
                .signInAndStay()
                .verifyInvalidCredentialsErrorMessage();
    }
}
