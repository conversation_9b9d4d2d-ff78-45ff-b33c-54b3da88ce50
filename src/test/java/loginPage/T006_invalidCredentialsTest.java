package loginPage;

import org.junit.jupiter.api.Test;
import pages.webPages.LandingPage;
import pages.webPages.LoginPage;
import utils.BaseTest;
import xray.Xray;

/**
 * This test case should verify that users can not login if they enter invalid credentials.
 *
 * <AUTHOR>
 */
public class T006_invalidCredentialsTest extends BaseTest {

    @Test
    @Xray(key = "XCAPITAIN-1545")
    public void invalidCredentials() {
        LandingPage landingPage = new LandingPage();
        LoginPage loginPage = landingPage.login();

        loginPage.setUsername("<EMAIL>")
                .setPassword("Tester-12333L")
                .signInAndStay()
                .verifyInvalidCredentialsErrorMessage();
    }
}
