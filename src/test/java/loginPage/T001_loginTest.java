package loginPage;

import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.webPages.LandingPage;
import pages.webPages.LoginPage;
import utils.BaseTest;
import xray.Xray;

/**
 * This test case should verify the happy path - login in to the application with a valid credentials of a registered user account.
 *
 * <AUTHOR>
 */
public class T001_loginTest extends BaseTest {

    @Test
    @Xray(key = "XCAPITAIN-160")
    public void login() {
        LandingPage landingPage = new LandingPage();
        LoginPage loginPage = landingPage.loginBtnDisplayed()
                .verifyEurodataTextMessage()
                .verifyLoginBtnText()
                .forgotBtnDisplayed()
                .verifyForgotPasswordLinkText()
                .login();

        DashboardPage dashboardPage = loginPage.setUsername("<EMAIL>")
                .setPassword("Tester-123")
                .signIn();

        dashboardPage.verifyDashboardTabDisplayed();
    }
}
