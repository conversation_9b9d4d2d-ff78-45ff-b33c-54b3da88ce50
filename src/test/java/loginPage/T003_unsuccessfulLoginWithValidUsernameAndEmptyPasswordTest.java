package loginPage;

import org.junit.jupiter.api.Test;
import pages.webPages.LandingPage;
import pages.webPages.LoginPage;
import utils.BaseTest;
import xray.Xray;

/**
 * This test case should verify that users can not login if they enter a valid username, click the "Sign in" button,
 * and leave the password field empty.
 *
 * <AUTHOR>
 */
public class T003_unsuccessfulLoginWithValidUsernameAndEmptyPasswordTest extends BaseTest {

    @Test
    @Xray(key = "XCAPITAIN-1542")
    public void unsuccessfulLoginWithValidUsernameAndEmptyPassword() {
        LandingPage landingPage = new LandingPage();
        LoginPage loginPage = landingPage.login();

        loginPage.setUsername("<EMAIL>")
                .signInAndStay()
                .verifyInvalidCredentialsErrorMessage();
    }
}
