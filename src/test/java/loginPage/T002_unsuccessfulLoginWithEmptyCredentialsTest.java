package loginPage;

import org.junit.jupiter.api.Test;
import pages.webPages.LandingPage;
import pages.webPages.LoginPage;
import utils.BaseTest;
import xray.Xray;

/**
 * This test case should verify that users can not log in if they click the "Sign in" button and leave empty username and
 * password fields.
 *
 * <AUTHOR>
 */
public class T002_unsuccessfulLoginWithEmptyCredentialsTest extends BaseTest {

    @Test
    @Xray(key = "XCAPITAIN-1541")
    public void unsuccessfulLoginWithEmptyCredentials() {
        LandingPage landingPage = new LandingPage();
        LoginPage loginPage = landingPage.login();

        loginPage.signInAndStay()
                .verifyInvalidCredentialsErrorMessage();
    }
}
