package paymentScreen;

import constants.ConstantsFile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.AddCounterpartIbanAndBicDialog;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that a user can open the IBAN pop-up dialog from the Payment screen by clicking the IBAN field
 * and successfully edit the IBAN, with the updated value displayed on the Payment screen.
 *
 * <AUTHOR>
 */
public class T008_editIbanTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1774")
    public void editIban() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        AddCounterpartIbanAndBicDialog addCounterpartIbanAndBicDialog = paymentPage.clickIbanField();
        addCounterpartIbanAndBicDialog.setIbanOrBic(ConstantsFile.VALID_IBAN_I)
                .selectNewValue()
                .verifySelectedNewValue(ConstantsFile.VALID_IBAN_I)
                .clickSaveButton();

        paymentPage.verifyIbanValue(ConstantsFile.VALID_IBAN_I)
                .verifyBicValue(ConstantsFile.VALID_GENERATED_BIC);

        paymentPage.clickIbanField();
        addCounterpartIbanAndBicDialog.setIbanOrBic(ConstantsFile.VALID_IBAN_II)
                .selectNewValue()
                .verifySelectedNewValue(ConstantsFile.VALID_IBAN_II)
                .clickSaveButton();

        paymentPage.verifyIbanValue(ConstantsFile.VALID_IBAN_II)
                .verifyBicValue(ConstantsFile.VALID_BIC_II);
    }
}
