package paymentScreen;

import constants.ConstantsFile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.AddCounterpartIbanAndBicDialog;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that all text on the IBAN dialog is displayed correctly.
 * The test includes verifying the title, new IBAN value, save button, and close button text.
 *
 * <AUTHOR> <PERSON>
 */
public class T014_verifyAllTextOnTheIbanDialogDisplayedCorrectlyTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1780")
    public void verifyAllTextOnTheIbanDialogDisplayedCorrectly() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        AddCounterpartIbanAndBicDialog addCounterpartIbanAndBicDialog = paymentPage.clickIbanField();
        addCounterpartIbanAndBicDialog.verifyIbanDialogTitleText()
                .setIbanOrBic(ConstantsFile.EMPTY_STRING)
                .verifyNewIbanValueText(ConstantsFile.EMPTY_STRING)
                .verifySaveButtonText()
                .verifyCloseButtonText();
    }
}
