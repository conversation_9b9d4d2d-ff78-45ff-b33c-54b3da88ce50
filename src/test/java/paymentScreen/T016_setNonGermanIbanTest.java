package paymentScreen;

import constants.ConstantsFile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.AddCounterpartIbanAndBicDialog;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that a user can open the IBAN pop-up dialog from the Payment screen by clicking the IBAN field
 * and successfully add a new non-German IBAN (Nederland IBAN), and verify that BIC is not generated.
 *
 * <AUTHOR>
 */

// TODO : Research this case and check the expected behavior when the user types the IBAN that is not German.
//  Also research if we have German IBAN that can not generate BIC.
public class T016_setNonGermanIbanTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1767")
    public void setNonGermanIban() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        paymentPage.clearBicField();

        AddCounterpartIbanAndBicDialog addCounterpartIbanAndBicDialog = paymentPage.clickIbanField();
        addCounterpartIbanAndBicDialog.setIbanOrBic(ConstantsFile.NL_IBAN)
                .selectNewValue()
                .verifySelectedNewValue(ConstantsFile.NL_IBAN)
                .clickSaveButton();

        paymentPage.verifyIbanValue(ConstantsFile.NL_IBAN)
                .verifyEmptyBicField();
    }
}
