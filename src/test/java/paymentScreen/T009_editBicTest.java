package paymentScreen;

import constants.ConstantsFile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.AddCounterpartIbanAndBicDialog;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that a user can add a BIC on the payment page.
 * The test includes adding a BIC, verifying its value, and updating it.
 *
 * <AUTHOR>
 */
public class T009_editBicTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1775")
    public void editBic() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        AddCounterpartIbanAndBicDialog addCounterpartIbanAndBicDialog = paymentPage.clickBicField();
        addCounterpartIbanAndBicDialog.setIbanOrBic(ConstantsFile.VALID_BIC_I)
                .selectNewValue()
                .verifySelectedNewValue(ConstantsFile.VALID_BIC_I)
                .clickSaveButton();

        paymentPage.verifyBicValue(ConstantsFile.VALID_BIC_I);
        paymentPage.clickBicField()
                .setIbanOrBic(ConstantsFile.VALID_BIC_II)
                .selectNewValue()
                .verifySelectedNewValue(ConstantsFile.VALID_BIC_II)
                .clickSaveButton();

        paymentPage.verifyBicValue(ConstantsFile.VALID_BIC_II);
    }
}
