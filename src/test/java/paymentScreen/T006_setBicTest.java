package paymentScreen;

import constants.ConstantsFile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.AddCounterpartIbanAndBicDialog;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that a user can open the BIC pop-up dialog from the Payment screen by clicking the BIC field,
 * successfully adding a new BIC that is displayed on the Payment screen.
 *
 * <AUTHOR> <PERSON>
 */
public class T006_setBicTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1768")
    public void setBic() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        AddCounterpartIbanAndBicDialog addCounterpartIbanAndBicDialog = paymentPage.clickBicField();
        addCounterpartIbanAndBicDialog.setIbanOrBic(ConstantsFile.VALID_BIC_I)
                .selectNewValue()
                .verifySelectedNewValue(ConstantsFile.VALID_BIC_I)
                .clickSaveButton();

        paymentPage.verifyBicValue(ConstantsFile.VALID_BIC_I);
    }
}
