package paymentScreen;

import api.apiConstants.ApiConstants;
import api.apiConstants.PrivilegeAction;
import api.services.Api;
import api.services.UserPrivilegeService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case should verify that when a user without a linked bank account and without bank management privileges navigates to the invoice list,
 * and the list contains at least one invoice with a "Payable" status, clicking the payment button for that invoice opens the payment screen.
 * The payment screen should display a placeholder with the text "To pay the invoice, please first link your bank account" and a disabled "Link bank account" button below it.
 *
 * <AUTHOR>
 */
public class T002_userWithoutBankAccAndManageBankAccPrivilegesPaymentScreenTest extends TestPrepare {

    Api api = Api.getInstance(getUsername("STANDARD_USER_WITH_ALL_PRIVILEGES"));

    UserPrivilegeService userPrivilegeService = new UserPrivilegeService(api);

    @BeforeEach
    public void prepare() throws Exception {
        userPrivilegeService.updateManageBankAccountPrivilege(PrivilegeAction.DISABLE, ApiConstants.NO_BANK_USER_ID);

        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1674")
    public void userWithoutBankAccAndManageBankAccPrivilegesPaymentScreen() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        invoicesPage.verifyPayableIcon();

        PaymentPage paymentPage = invoicesPage.clickFirstPayableIconInList();
        paymentPage.verifyLinkBankAccountBtnIsDisabled()
                .verifyNotLinkedMessageTxt();
    }

    @AfterEach
    public void cleanUp() {
        userPrivilegeService.updateManageBankAccountPrivilege(PrivilegeAction.ENABLE, ApiConstants.NO_BANK_USER_ID);
    }
}
