package paymentScreen;

import constants.ConstantsFile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.AddCounterpartIbanAndBicDialog;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that a user can open the BIC pop-up dialog from the Payment screen by clicking the BIC field,
 * but will be unable to successfully add a new BIC if it is invalid.
 * The user should receive an error message indicating that the BIC is invalid, and the "Save" button should remain disabled.
 *
 * <AUTHOR>
 */
public class T007_unsuccessfulAddingBicTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1773")
    public void unsuccessfulAddingBic() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        AddCounterpartIbanAndBicDialog addCounterpartIbanAndBicDialog = paymentPage.clickBicField();
        addCounterpartIbanAndBicDialog.setIbanOrBic(ConstantsFile.INVALID_BIC)
                .selectNewValue()
                .verifyInvalidBicErrorMessage()
                .verifySaveBtnDisabled();
    }
}
