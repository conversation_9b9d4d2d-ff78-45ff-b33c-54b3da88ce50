package paymentScreen;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that when a user is on the payment page for an invoice with all necessary fields displayed,
 * attempting payment after clearing mandatory fields triggers the display of appropriate error messages for each empty field.
 *
 * <AUTHOR>
 */
public class T003_allMandatoryErrorMessagesForRequiredFieldsOnThePaymentScreenAreDisplayedTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1685")
    public void allMandatoryErrorMessagesForRequiredFieldsOnThePaymentScreenAreDisplayed() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        paymentPage.clearAmountField()
                .clearIbanField()
                .clearBicField()
                .clearCounterpartNameField()
                .clearPurposeField()
                .clickPayBtnAndStay()
                .verifyAmountRequiredErrorMessage()
                .verifyIbanRequiredErrorMessage()
                .verifyBicRequiredErrorMessage()
                .verifyCounterpartNameRequiredErrorMessage()
                .verifyPurposeRequiredErrorMessage();
    }
}
