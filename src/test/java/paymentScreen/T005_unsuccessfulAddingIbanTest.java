package paymentScreen;

import constants.ConstantsFile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.AddCounterpartIbanAndBicDialog;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that a user can open the IBAN pop-up dialog from the Payment screen by clicking the IBAN field,
 * but will be unable to successfully add a new IBAN if it is invalid.
 * The user should receive an error message indicating that the IBAN is invalid, and the "Save" button should remain disabled.
 *
 * <AUTHOR> <PERSON>
 */
public class T005_unsuccessfulAddingIbanTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1770")
    public void unsuccessfulAddingIban() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        AddCounterpartIbanAndBicDialog addCounterpartIbanAndBicDialog = paymentPage.clickIbanField();
        addCounterpartIbanAndBicDialog.setIbanOrBic(ConstantsFile.INVALID_IBAN)
                .selectNewValue()
                .verifyInvalidIbanErrorMessage()
                .verifySaveBtnDisabled();
    }
}
