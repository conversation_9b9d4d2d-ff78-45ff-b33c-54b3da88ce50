package paymentScreen;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that a user can edit the purpose field on the payment page.
 * The test includes editing the purpose field, verifying its value, and updating it.
 *
 * <AUTHOR>
 */
public class T017_editPurposeTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1782")
    public void editPurpose() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        paymentPage.setPurposeField("Test")
                .verifyPurposeField("Test");
    }
}
