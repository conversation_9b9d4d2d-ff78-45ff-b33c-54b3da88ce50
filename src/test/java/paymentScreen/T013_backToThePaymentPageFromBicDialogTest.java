package paymentScreen;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.AddCounterpartIbanAndBicDialog;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that clicking the system "Back" button on the BIC pop-up dialog from the Payment screen
 * (or swiping from left to right on iOS) closes the pop-up and returns the user to the Payment screen.
 *
 * <AUTHOR> <PERSON>
 */
public class T013_backToThePaymentPageFromBicDialogTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1779")
    public void backToThePaymentPageFromBicDialog() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        AddCounterpartIbanAndBicDialog addCounterpartIbanAndBicDialog = paymentPage.clickBicField()
                .verifyBicDialogTitleText();
        addCounterpartIbanAndBicDialog.back();

        paymentPage.verifyPayBtnDisplayed();
    }
}
