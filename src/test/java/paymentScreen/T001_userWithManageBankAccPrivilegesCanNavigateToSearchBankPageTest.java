package paymentScreen;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.bankPages.BankAccountsSearchPage;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.invoicesPages.InvoicesFiltersPage;
import pages.invoicesPages.InvoicesPage;
import pages.paymentPages.PaymentPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case should verify that when a user without a linked bank account but with managing bank account privileges,
 * navigates to the invoice list, and the list contains at least one invoice with a "Payable" status, clicking the payment button for that invoice opens the payment screen.
 * The payment screen should display a placeholder with the text "To pay the invoice, please first link your bank account" and a button below it to add a bank account.
 *
 * <AUTHOR>
 */
public class T001_userWithManageBankAccPrivilegesCanNavigateToSearchBankPageTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1667")
    public void userWithManageBankAccPrivilegesCanNavigateToSearchBankPage() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();

        InvoicesFiltersPage invoicesFiltersPage = invoicesPage.filtersBtn();
        invoicesFiltersPage.statusBtn()
                .payableStatusBtn()
                .applyFilterBtn();

        PaymentPage paymentPage = invoicesPage.verifyPayableIcon()
                .clickFirstPayableIconInList();

        BankAccountsSearchPage bankAccountsSearchPage = paymentPage
                .verifyNotLinkedMessageTxt()
                .verifyLinkBankAccountBtnIsEnabled()
                .clickLinkBankAccountBtn();

        bankAccountsSearchPage.verifySearchBarIsDisplayed();
    }
}
