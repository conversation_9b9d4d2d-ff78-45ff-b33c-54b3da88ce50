package settings.user;

import api.apiConstants.ApiConstants;
import api.services.Api;
import api.services.UserSettingsService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.settingsPages.SettingsPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test should verify if the user can change language from EN to ES and back to EN within Settings / User.
 *
 * <AUTHOR>
 */
public class T003_changingLanguageTestEnToEsToEnTest extends TestPrepare {

    Api api = Api.getInstance(getUsername("STANDARD_USER_WITH_ALL_PRIVILEGES"));

    UserSettingsService userSettingsService = new UserSettingsService(api);

    @BeforeEach
    public void prepare() throws Exception {
        userSettingsService.setLanguage(ApiConstants.AUTOMATION_COMPANY_ID, ApiConstants.AUTOMATION_USER_ID, ApiConstants.ENGLISH);

        loginWithStandardUserWithAllPrivileges();
    }

    @Test
    @Xray(key = "XCAPITAIN-1683")
    public void changingLanguageTestEnToEsToEn() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        SettingsPage settings = mainMenuPage.settingsBtn();

        settings.userTab()
                .chooseLanguage()
                .lngListEs()
                .saveBtn()
                .confirmChangingLanguage();

        dashboardPage.verifyDashboardTabDisplayed()
                .verifyDashboardHeaderText("Resumen")
                .hamburgerBtn();

        mainMenuPage.settingsBtn();

        settings.userTab()
                .chooseLanguage()
                .lngListEn()
                .saveBtn()
                .confirmChangingLanguage();

        dashboardPage.verifyDashboardTabDisplayed()
                .verifyDashboardHeaderText("Dashboard");
    }

    @AfterEach
    public void cleanUp() {

        userSettingsService.setLanguage(ApiConstants.AUTOMATION_COMPANY_ID, ApiConstants.AUTOMATION_USER_ID, ApiConstants.ENGLISH);
    }
}
