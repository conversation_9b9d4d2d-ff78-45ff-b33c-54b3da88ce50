package mainMenu;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.webPages.LandingPage;
import pages.navigationPages.MainMenuPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test should verify whether user can log out from the app.
 *
 * <AUTHOR>
 */
public class T001_logoutTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithStandardUserWithAllPrivileges();
    }

    @Test
    @Xray(key = "XCAPITAIN-177")
    public void logout() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        LandingPage landingPage = mainMenuPage.logout()
                .verifyInfoDialogTitle()
                .verifyInfoDialogMessage()
                .verifyInfoDialogLogoutBtnText()
                .verifyInfoDialogCancelBtnText()
                .logoutOnInfoDialog();

        landingPage.loginBtnDisplayed();
    }
}
