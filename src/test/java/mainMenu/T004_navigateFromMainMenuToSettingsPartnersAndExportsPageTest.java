package mainMenu;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.exportsPages.ExportsPage;
import pages.partnerMasterdataPages.PartnerMasterdataPage;
import pages.settingsPages.SettingsPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case should verify that the user can navigate to Settings, Partner masterdata and Exports from the main menu page.
 * When the user logs in and opens the main menu they need to navigate to Settings, Partner masterdata and Exports from the main menu page in this order:
 * 1. Settings
 * 2. Partner masterdata
 * 3. Exports
 *
 * <AUTHOR> <PERSON>
 */
public class T004_navigateFromMainMenuToSettingsPartnersAndExportsPageTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithStandardUserWithAllPrivileges();
    }

    @Test
    @Xray(key = "XCAPITAIN-1433")
    public void navigateFromMainMenuToSettingsPartnersAndExportsPage() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        SettingsPage settingsPage = mainMenuPage.settingsBtn();

        settingsPage.verifySettingsHeader()
                .backToDashboard();

        dashboardPage.hamburgerBtn();

        ExportsPage exportsPage = mainMenuPage.exportsBtn();

        exportsPage.verifyExportsHeader()
                .backToDashboard();

        dashboardPage.hamburgerBtn();

        PartnerMasterdataPage partnerMasterdataPage = mainMenuPage.partnerBtn();

        partnerMasterdataPage.verifyPartnerMasterdataHeader();
    }
}