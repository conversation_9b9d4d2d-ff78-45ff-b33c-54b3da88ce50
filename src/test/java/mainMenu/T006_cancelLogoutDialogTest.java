package mainMenu;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import prepare.TestPrepare;

/**
 * This test verify that user is back on the 'Main menu' page after canceling on logout 'Info' dialog
 *
 * <AUTHOR>
 */
public class T006_cancelLogoutDialogTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithStandardUserWithAllPrivileges();
    }

    @Test
    public void cancelOnLogoutDialog() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        mainMenuPage.logout()
                .verifyInfoDialogTitle()
                .verifyInfoDialogMessage()
                .verifyInfoDialogLogoutBtnText()
                .verifyInfoDialogCancelBtnText()
                .cancelOnInfoDialog()
                .verifyLogoutBtnVisible();
    }
}
