package mainMenu;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import prepare.TestPrepare;

/**
 * This test verifies that when the user taps on "Send Feedback", the 'Compose Report' dialog opens. Then verifies that upon
 * tapping "Compose Report", the email app launches with the "To" field pre-<NAME_EMAIL>.
 *
 * <AUTHOR>
 */
public class T007_composeReportTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithStandardUserWithAllPrivileges();
    }

    @Test
    public void composeReport() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        mainMenuPage.expandArrowBtn()
                .sendFeedback()
                .verifySendFeedbackDialogTitle()
                .verifySendFeedbackDialogMessage()
                .verifySendFeedbackCancelBtnVisible()
                .verifySendFeedbackComposeReportBtnVisible()
                .sendFeedbackComposeReportBtn()
                .verifySendFeedBackEmailOpened()
                .verifyCapitainEmailIsPredifined();
    }
}
