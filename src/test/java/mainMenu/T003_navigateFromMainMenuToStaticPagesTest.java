package mainMenu;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.webPages.StaticPages;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case should verify that users can navigate to Data privacy, Imprint, and F.A.Q. page's from the main menu page.
 * When the user logs in and opens the main menu they need to click on the "bottom" arrow in the main menu and after that they
 * can access the 'F.A.Q.', 'Impresum' and 'Data privacy' page's.
 *
 * <AUTHOR>
 */
public class T003_navigateFromMainMenuToStaticPagesTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithStandardUserWithAllPrivileges();
    }

    @Test
    @Xray(key = "XCAPITAIN-1434")
    public void navigateFromMainMenuToStaticPages() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        StaticPages staticPages = mainMenuPage.expandArrowBtn()
                .clickOnFAQBtn();

        staticPages.verifyURL("FAQ_URL_BAR")
                .backToDashboard()
                .hamburgerBtn();

        mainMenuPage.imprintBtn();

        staticPages.verifyURL("IMPRESUM_URL_BAR")
                .backToDashboard()
                .hamburgerBtn();

        mainMenuPage.dataPrivacyBtn();

        staticPages.verifyURL("DATA_PRIVACY_URL_BAR");
    }
}
