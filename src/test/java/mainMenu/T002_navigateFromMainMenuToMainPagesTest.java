package mainMenu;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import pages.bankPages.BankPage;
import pages.cashDeskPages.CashDeskPage;
import pages.documentsPages.DocumentsPage;
import pages.emailsPages.EmailsPage;
import pages.invoicesPages.InvoicesPage;
import pages.messagesPages.MessagesPage;
import pages.tasksPages.TasksPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case should verify that the user can navigate main pages from the main menu.
 * When the user logs in and opens the main menu they need to navigate through main pages in this order
 * (Messages, Bank, Invoices, Quests, Cash Desk, Emails, Documents) and navigate back to the "Dashboard" page.
 *
 * <AUTHOR>
 */
public class T002_navigateFromMainMenuToMainPagesTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1427")
    public void navigateFromMainMenuToMainPages() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        MessagesPage messagesPage = mainMenuPage.messagesBtn();
        messagesPage.verifyMessagesHeader()
                .hamburgerBtn();

        BankPage bankPage = mainMenuPage.bankBtn();
        bankPage.verifyBankHeader()
                .hamburgerBtn();

        InvoicesPage invoicesPage = mainMenuPage.invoicesBtn();
        invoicesPage.verifyInvoicesHeader()
                .hamburgerBtn();

        TasksPage tasksPage = mainMenuPage.tasksBtn();
        tasksPage.verifyTasksHeader()
                .hamburgerBtn();

        CashDeskPage cashDeskPage = mainMenuPage.cashDeskBtn();
        cashDeskPage.verifyCashDeskHeader()
                .hamburgerBtn();

        EmailsPage emailsPage = mainMenuPage.emailsBtn();
        emailsPage.verifyEmailsHeader()
                .hamburgerBtn();

        DocumentsPage documentsPage = mainMenuPage.documentsBtn();
        documentsPage.verifyDocumentsHeader()
                .hamburgerBtn();

        mainMenuPage.dashboardBtn();

        dashboardPage.verifyDashboardHeader();
    }
}
