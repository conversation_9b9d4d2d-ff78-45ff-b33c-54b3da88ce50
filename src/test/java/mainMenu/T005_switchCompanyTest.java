package mainMenu;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import prepare.TestPrepare;
import xray.Xray;

/**
 * This test case verifies that the user can switch the company.
 *
 * <AUTHOR>
 */
public class T005_switchCompanyTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithUser("<EMAIL>", "Tester-123");
    }

    @Test
    @Xray(key = "XCAPITAIN-1425")
    public void switchCompany() {
        DashboardPage dashboardPage = new DashboardPage();
        MainMenuPage mainMenuPage = dashboardPage.hamburgerBtn();

        String currentCompanyName = mainMenuPage.getCompanyNameText();

        mainMenuPage.openSwitchCompanyDialog()
                .switchCompany(currentCompanyName);

        dashboardPage.hamburgerBtn();

        String newCompanyName = mainMenuPage.getCompanyNameText();

        mainMenuPage.verifyCompanyNameSwitched(currentCompanyName, newCompanyName);
    }
}
