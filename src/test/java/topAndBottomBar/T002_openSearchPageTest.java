package topAndBottomBar;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.searchPages.SearchPage;
import prepare.TestPrepare;

/**
 * This test verifies that the user can open the search page from the dashboard page.
 *
 * <AUTHOR>
 */
public class T002_openSearchPageTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
    loginWithStandardUserWithAllPrivileges();
    }
    @Test
    public void openSearchPage() {
        DashboardPage dashboardPage = new DashboardPage();

        SearchPage searchPage = dashboardPage.searchBtn();
        searchPage.verifyBackButtonVisible()
                .verifySearchInputFieldVisible();
    }
}
