package topAndBottomBar;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.MainMenuPage;
import prepare.TestPrepare;

/**
 * This test verifies that the user can open the main menu from the dashboard page.
 *
 * <AUTHOR>
 */
public class T001_openMainMenuTest extends TestPrepare {

    @BeforeEach
    public void prepare() {
        loginWithStandardUserWithAllPrivileges();
    }

    @Test
    public void openMainMenu() {
        DashboardPage dashboardPage = new DashboardPage();

        MainMenuPage mainMenuPage = dashboardPage.verifyHamburgerBtnDisplayed()
                .hamburgerBtn();

        mainMenuPage.verifyCapitainLogoVisible()
                .verifyUserNameVisible()
                .verifyLogoutBtnVisible();
    }
}
