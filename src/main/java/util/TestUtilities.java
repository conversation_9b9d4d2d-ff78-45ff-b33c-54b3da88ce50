package util;

import constants.ConstantsFile;
import org.json.JSONObject;
import utils.BaseTest;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;

import static constants.ConstantsFile.CREDENTIALS_FILE_PATH;

public class TestUtilities extends BaseTest {

    /**
     * Loads and parses a JSON file into a {@link JSONObject}.
     *
     * @return A {@link JSONObject} representing the contents of the JSON file.
     * @throws RuntimeException if an I/O error occurs while reading the file.
     */
    private JSONObject loadJsonFromFile() {

        try {
            String content = Files.readString(Paths.get(CREDENTIALS_FILE_PATH));
            return new JSONObject(content);
        } catch (IOException e) {
            throw new RuntimeException("Failed to read JSON file: " + CREDENTIALS_FILE_PATH, e);
        }
    }

    /**
     * Retrieves a username from the JSON test data file based on the given property key.
     *
     * @param property The key representing the username in the JSON file.
     * @return The corresponding username as a {@link String}.
     * @throws RuntimeException if the specified username key is not found in the JSON file.
     */
    public String getUsername(String property) {
        String data = "";
        String testDataParent = "USERNAMES";

        JSONObject testDataObject = loadJsonFromFile().getJSONObject(testDataParent);
        if (testDataObject.has(property)) {
            data = testDataObject.getString(property);
        }
        if (data.isEmpty())
            throw new RuntimeException("Username '" + property + "' cannot be found.");
        return data;
    }

    /**
     * Retrieves a password from the JSON test data file based on the given property key.
     *
     * @param property The key representing the password in the JSON file.
     * @return The corresponding password as a {@link String}.
     * @throws RuntimeException if the specified password key is not found in the JSON file.
     */
    public String getPassword(String property) {
        String data = "";
        String testDataParent = "PASSWORDS";

        JSONObject testDataObject = loadJsonFromFile().getJSONObject(testDataParent);
        if (testDataObject.has(property)) {
            data = testDataObject.getString(property);
        }
        if (data.isEmpty())
            throw new RuntimeException("Password '" + property + "' cannot be found.");

        return data;
    }

    /**
     * Formats a LocalDate into "dd/MM/yyyy" string.
     */
    public String formatDMY(LocalDate date) {
        return date.format(ConstantsFile.DMY_FORMATTER);
    }

    /**
     * Formats a LocalDate into ISO format string "yyyy-MM-dd".
     */
    public String formatISO(LocalDate date) {
        return date.format(ConstantsFile.ISO_FORMATTER);
    }
}
