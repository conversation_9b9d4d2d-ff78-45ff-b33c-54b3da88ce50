package util;

import commands.Actions;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.WebElement;

import java.util.List;

public class Base extends Actions {

    /**
     * This method check platform version and backs to previous page with taping on the 'Back button' for Android,
     * and swiping from top to bottom for iOS platform
     */
    public void back() {
        if (isAndroid()) {
            getDriver().navigate().back();
        } else if (isIos()) {
            swipeTopToBottom();
        }
    }

    /**
     * Clears the content of the specified web element (input field) and types the provided text.
     *
     * @param element the web element to be cleared and typed into
     * @param text    the text to type into the web element after clearing it
     */
    protected void clearAndType(WebElement element, String text) {
        clear(element);
        type(element, text);
    }

    /**
     * Verifies that the given list has exactly the expected number of elements.
     *
     * @param elements      the WebElement list to check
     * @param expectedCount the expected number of items
     */
    protected void verifyNumberOfListItems(List<? extends WebElement> elements, int expectedCount) {
        int actualSize = elements.size();
        Assertions.assertEquals(expectedCount, actualSize);

    }
}
