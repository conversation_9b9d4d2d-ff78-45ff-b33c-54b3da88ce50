package api.models.cashDeskModels;

import api.apiConstants.ApiConstants;

import java.util.HashMap;
import java.util.Map;

public class ChangeCashAccountStatus {

    /**
     * Creates a map of input variables for deleting a cash account, identified by its ID.
     *
     * @param cashAccountId The unique identifier of the cash account to be deleted.
     * @return A map containing the input variables necessary for the GraphQL mutation to delete the cash account.
     */
    public static Map<String, Object> deleteCashAccount(String cashAccountId) {
        Map<String, Object> inputVariables = new HashMap<>();
        inputVariables.put("cashAccountId", cashAccountId);
        inputVariables.put("status", ApiConstants.DELETED);

        Map<String, Object> variables = new HashMap<>();
        variables.put("input", inputVariables);
        return variables;
    }

    /**
     * Creates a map of variables for changing the status of a cash account.
     *
     * @param cashAccountId The ID of the cash account to be updated.
     * @param status        The new status of the cash account.
     * @return A map containing the input variables required for the GraphQL mutation to change the status of the cash account.
     */
    public static Map<String, Object> changeCashAccountStatus(String cashAccountId, String status) {
        Map<String, Object> inputVariables = new HashMap<>();
        inputVariables.put("cashAccountId", cashAccountId);
        inputVariables.put("status", status);

        Map<String, Object> variables = new HashMap<>();
        variables.put("input", inputVariables);
        return variables;
    }
}
