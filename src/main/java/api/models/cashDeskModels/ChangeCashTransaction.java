package api.models.cashDeskModels;

import api.apiConstants.ApiConstants;

import java.util.HashMap;
import java.util.Map;

public class ChangeCashTransaction {

    /**
     * Creates a map of input variables needed to close a cash transaction.
     *
     * @param cashTransactionId the ID of the cash transaction to be closed
     * @return a map containing the input variables for the GraphQL mutation to close the transaction
     */
    public static Map<String, Object> closeCashTransaction(String cashTransactionId) {

        Map<String, Object> inputVariables = new HashMap<>();
        inputVariables.put("id", cashTransactionId);
        inputVariables.put("status", ApiConstants.CLOSED);

        Map<String, Object> variables = new HashMap<>();
        variables.put("input", inputVariables);
        return variables;
    }

    /**
     * Creates a map of variables needed to delete a cash transaction.
     *
     * @param cashTransactionId The ID of the cash transaction to be deleted.
     * @return A map containing the input variables for the GraphQL mutation to delete the transaction.
     */
    public static Map<String, Object> deleteCashTransaction(String cashTransactionId) {

        Map<String, Object> inputVariables = new HashMap<>();
        inputVariables.put("id", cashTransactionId);
        inputVariables.put("status", ApiConstants.DELETED);

        Map<String, Object> variables = new HashMap<>();
        variables.put("input", inputVariables);
        return variables;
    }
}
