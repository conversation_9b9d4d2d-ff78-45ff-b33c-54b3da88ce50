package api.models.cashDeskModels;

import java.util.HashMap;
import java.util.Map;

public class CreateCashTransaction {

    /**
     * Creates a map of variables for creating a cash transaction.
     *
     * @param cashAccountAssignedId The assigned ID of the cash account for the transaction.
     * @param payDate               The date of the payment.
     * @param payNote               A note or description for the payment.
     * @param amount                The amount of the transaction.
     * @return A map of variables formatted for a GraphQL mutation to create a cash transaction.
     */
    public static Map<String, Object> createCashTransaction(String cashAccountAssignedId, String payDate, String payNote, int amount) {

        Map<String, Object> inputVariables = new HashMap<>();
        inputVariables.put("cashAccountId", cashAccountAssignedId);
        inputVariables.put("payDate", payDate);
        inputVariables.put("payNote", payNote);
        inputVariables.put("amount", "EUR " + amount);

        Map<String, Object> variables = new HashMap<>();
        variables.put("input", inputVariables);
        return variables;
    }
}
