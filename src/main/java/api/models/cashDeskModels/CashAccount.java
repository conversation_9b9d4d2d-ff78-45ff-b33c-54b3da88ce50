package api.models.cashDeskModels;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

public class CashAccount {

    private static final String NAME = "Automation account - " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yy/MM/dd HH:mm:ss"));
    private static final String STATUS = "ACTIVE";
    private static final String TYPE = "MANUAL";

    /**
     * Gets the name of the cash desk account.
     *
     * @return The name of the cash desk account.
     */
    public static String getName() {
        return NAME;
    }

    /**
     * Creates a map of input variables that can be used to create a cash desk account in the GraphQL API.
     *
     * @param startDate    The start date of the cash desk account to be created.
     * @param startBalance The starting balance of the cash desk account to be created.
     * @param currency     The currency of the cash desk account to be created.
     * @return A map containing the input variables required for the GraphQL mutation to create a cash desk account.
     */
    public static Map<String, Object> createCashAccount(String startDate, int startBalance, String currency) {
        Map<String, Object> inputVariables = new HashMap<>();
        inputVariables.put("name", NAME);
        inputVariables.put("startDate", startDate);
        inputVariables.put("startBalance", currency + " " + startBalance);
        inputVariables.put("currency", currency);
        inputVariables.put("status", STATUS);
        inputVariables.put("type", TYPE);

        Map<String, Object> variables = new HashMap<>();
        variables.put("input", inputVariables);
        return variables;
    }
}
