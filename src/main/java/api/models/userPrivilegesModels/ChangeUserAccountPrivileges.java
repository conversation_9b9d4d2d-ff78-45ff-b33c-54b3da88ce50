package api.models.userPrivilegesModels;

import java.util.HashMap;
import java.util.Map;

public class ChangeUserAccountPrivileges {

    private static final String ASSIGNED_ROLE = "EMPTY_USER";
    private static final int[] MANAGE_BANK_ACC_OFF = {32, 1, 6, 7, 8, 9, 41, 42, 16, 18, 19, 20, 21, 22, 24, 26, 27, 28, 31};
    private static final int[] MANAGE_BANK_ACC_ON = {32, 1, 6, 7, 8, 9, 41, 42, 15, 16, 18, 19, 20, 21, 22, 24, 26, 27, 28, 31};

    /**
     * Creates a map of variables for turning off the "Manage bank account" privilege for a user.
     *
     * @param userId The ID of the user whose privilege is to be turned off.
     * @return A map containing the input variables for the GraphQL mutation to turn off the privilege.
     */
    public static Map<String, Object> turnOffManageBankAccPrivilege(String userId) {
        Map<String, Object> inputVariables = new HashMap<>();
        inputVariables.put("userId", userId);
        inputVariables.put("assignedRole", ASSIGNED_ROLE);
        inputVariables.put("customPrivilegeIds", MANAGE_BANK_ACC_OFF);

        Map<String, Object> variables = new HashMap<>();
        variables.put("input", inputVariables);
        return variables;
    }

    /**
     * Creates a map of variables for turning on the "Manage bank account" privilege for a user.
     *
     * @param userId The ID of the user whose privilege is to be turned on.
     * @return A map containing the input variables for the GraphQL mutation to turn on the privilege.
     */
    public static Map<String, Object> turnOnManageBankAccPrivilege(String userId) {
        Map<String, Object> inputVariables = new HashMap<>();
        inputVariables.put("userId", userId);
        inputVariables.put("assignedRole", ASSIGNED_ROLE);
        inputVariables.put("customPrivilegeIds", MANAGE_BANK_ACC_ON);

        Map<String, Object> variables = new HashMap<>();
        variables.put("input", inputVariables);
        return variables;
    }
}
