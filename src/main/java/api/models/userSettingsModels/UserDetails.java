package api.models.userSettingsModels;

import java.util.HashMap;
import java.util.Map;

public class UserDetails {
    private static final String GIVEN_NAME = "Automation";
    private static final String PERSONAL_TITLE = "MR";
    private static final String SURNAME = "Admin";
    private static final String PHONE = null;

    public static Map<String, Object> setLanguage(String userId, String language) {
        Map<String, Object> inputVariables = new HashMap<>();
        inputVariables.put("id", userId);
        inputVariables.put("givenName", GIVEN_NAME);
        inputVariables.put("personalTitle", PERSONAL_TITLE);
        inputVariables.put("surname", SURNAME);
        inputVariables.put("phone", PHONE);
        inputVariables.put("language", language);

        Map<String, Object> variables = new HashMap<>();
        variables.put("input", inputVariables);
        return variables;
    }
}
