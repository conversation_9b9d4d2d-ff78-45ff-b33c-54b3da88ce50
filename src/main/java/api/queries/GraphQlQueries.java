package api.queries;

public class GraphQlQueries {

    // User Details Queries Section

    public static final String UPDATE_USER_DETAILS = """
                mutation updateUserDetail($input: UpdateUser!) {
                    updateUser(input: $input) {
                        user {
                            id
                        }
                    }
                }
            """;

    // Cash Desk Queries Section

    public static final String CREATE_CASH_ACCOUNT = """
             mutation createCashAccount($input: CreateCashAccount!) {
                    createCashAccount(input: $input) {
                        assignedId
                    }
                }
            """;

    public static final String CHANGE_CASH_ACCOUNT_STATUS = """
             mutation changeCashAccountStatus($input: ChangeCashAccountStatus!) {
               changeCashAccountStatus(input: $input) {
                 cashAccount {
                   id
                 }
               }
             }
            """;

    public static final String CREATE_CASH_TRANSACTION = """
             mutation createCashTransactionV2($input: CreateCashTransaction!) {
              createCashTransactionV2(input: $input) {
                ... on CreateCashTransactionResponse {
                  cashTransaction {
                    id
                  }
                }
                ... on Error {
                  errorCode
                  errorParams {
                    key
                    value
                  }
                  requestId
                  timestamp
                }
              }
            }
            """;

    public static final String CHANGE_CASH_TRANSACTION_STATUS = """
            mutation changeCashTransactionStatus($input: ChangeCashTransactionStatusInput!) {
              changeCashTransactionStatus(input: $input) {
                cashTransaction {
                  id
                }
              }
            }
            """;

    // User account privileges

    public static final String CHANGE_USER_ACCOUNT_PRIVILEGES = """
                mutation changeUserAccountPrivileges($input: ChangeUserAccountPrivileges!) {
                changeUserAccountPrivileges(input: $input) {
                    userId
                    assignedRole
                    customPrivilegeIds
                }
            }
            """;
}
