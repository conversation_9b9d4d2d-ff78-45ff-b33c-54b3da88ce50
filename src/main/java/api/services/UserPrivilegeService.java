package api.services;

import api.apiConstants.ApiConstants;
import api.apiConstants.PrivilegeAction;
import api.models.userPrivilegesModels.ChangeUserAccountPrivileges;
import api.queries.GraphQlQueries;
import logger.ConsoleLogger;
import utils.GraphqlClient;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

public class UserPrivilegeService {

    protected static final Logger LOGGER = ConsoleLogger.getLogger();
    private final GraphqlClient graphqlClient;

    public UserPrivilegeService(Api api) {
        this.graphqlClient = api.getGraphqlClient();
    }

    /**
     * Enables or disables the "Manage bank account" privilege for a user.
     *
     * @param action The action to be performed on the privilege.
     * @param userId The ID of the user to be updated.
     */
    public void updateManageBankAccountPrivilege(PrivilegeAction action, String userId) {

        Map<String, String> headers = new HashMap<>();
        headers.put("selected-company", "1ef92b7a-f92e-6dfc-afdb-171e1335497c");

        if (action == PrivilegeAction.ENABLE) {

            try {
                Map<String, Object> variables = ChangeUserAccountPrivileges.turnOnManageBankAccPrivilege(userId);

                String response = graphqlClient.executeQuery(
                        GraphQlQueries.CHANGE_USER_ACCOUNT_PRIVILEGES,
                        variables,
                        ApiConstants.MASTERDATA_ENDPOINT, headers);

                LOGGER.info("GraphQL Response: " + response);
            } catch (Exception e) {
                LOGGER.warning("Failed to turn on 'Manage bank account' privilege: " + e);
            }
        } else if (action == PrivilegeAction.DISABLE) {
            try {
                Map<String, Object> variables = ChangeUserAccountPrivileges.turnOffManageBankAccPrivilege(userId);

                String response = graphqlClient.executeQuery(
                        GraphQlQueries.CHANGE_USER_ACCOUNT_PRIVILEGES,
                        variables,
                        ApiConstants.MASTERDATA_ENDPOINT, headers);

                LOGGER.info("GraphQL Response: " + response);
            } catch (Exception e) {
                LOGGER.warning("Failed to turn off 'Manage bank account' privilege: " + e);
            }
        }
    }
}
