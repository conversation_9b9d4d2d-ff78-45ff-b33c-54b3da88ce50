package api.services;

import api.apiConstants.ApiConstants;
import api.models.cashDeskModels.CashAccount;
import api.models.cashDeskModels.ChangeCashAccountStatus;
import api.models.cashDeskModels.ChangeCashTransaction;
import api.models.cashDeskModels.CreateCashTransaction;
import api.queries.GraphQlQueries;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import logger.ConsoleLogger;
import utils.GraphqlClient;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

public class CashDeskService {

    protected static final Logger LOGGER = ConsoleLogger.getLogger();
    private final GraphqlClient graphqlClient;
    private final Map<String, String> cashDeskHeaders = new HashMap<>();


    public CashDeskService(Api api) {
        this.graphqlClient = api.getGraphqlClient();
    }

    public String getCashDeskName() {
        return CashAccount.getName();
    }

    /**
     * Creates a cash desk account by executing a GraphQL mutation.
     *
     * @param companyId    The ID of the company for which the cash desk account is being created.
     * @param startDate    The start date for the cash desk account.
     * @param startBalance The starting balance of the cash desk account to be created.
     * @param currency     The currency of the cash desk account to be created.
     * @return The ID of the newly created cash desk account, or null if the ID is not found or an error occurs.
     */
    public String createCashDeskAccount(String companyId, String startDate, int startBalance, String currency) {

        cashDeskHeaders.put("selected-company", companyId);

        try {
            Map<String, Object> variables = CashAccount.createCashAccount(startDate, startBalance, currency);

            String response = graphqlClient.executeQuery(
                    GraphQlQueries.CREATE_CASH_ACCOUNT,
                    variables,
                    ApiConstants.CASH_DESK_ENDPOINT, cashDeskHeaders);

            LOGGER.info("GraphQL Response: " + response);

            String assignedId = getCashAccountAssignedId(response);
            if (assignedId != null) {
                LOGGER.info("Created Cash Desk Account ID: " + assignedId);
                return assignedId;
            }
        } catch (Exception e) {
            LOGGER.warning("Failed to create cash account! - " + e.getMessage());
        }
        return null;
    }

    /**
     * Deletes a cash desk account by executing a GraphQL mutation to change its status.
     *
     * @param cashAccountId The ID of the cash desk account to be deleted.
     */
    public void deleteCashDeskAccount(String cashAccountId) {

        try {
            Map<String, Object> variables = ChangeCashAccountStatus.deleteCashAccount(cashAccountId);

            String response = graphqlClient.executeQuery(
                    GraphQlQueries.CHANGE_CASH_ACCOUNT_STATUS,
                    variables,
                    ApiConstants.CASH_DESK_ENDPOINT,
                    cashDeskHeaders);

            LOGGER.info("GraphQL Response: " + response);
        } catch (Exception e) {
            LOGGER.warning("Failed to delete cash account with ID: " + cashAccountId + " - " + e.getMessage());
        }
    }

    /**
     * Creates a cash transaction by executing a GraphQL mutation and returning the transaction ID.
     *
     * @param cashAccountAssignedId The assigned ID of the cash account for the transaction.
     * @param payDate               The date of the payment.
     * @param payNote               A note or description for the payment.
     * @param amount                The amount of the transaction.
     * @return The transaction ID of the created cash transaction, or null if the creation fails.
     */
    public String createCashTransaction(String cashAccountAssignedId, String payDate, String payNote, int amount) {

        try {
            Map<String, Object> variables = CreateCashTransaction.createCashTransaction(cashAccountAssignedId, payDate, payNote, amount);

            String response = graphqlClient.executeQuery(
                    GraphQlQueries.CREATE_CASH_TRANSACTION,
                    variables,
                    ApiConstants.CASH_DESK_ENDPOINT, cashDeskHeaders);

            LOGGER.info("GraphQL Response: " + response);

            String transactionId = getCashTransactionId(response);
            if (transactionId != null) {
                LOGGER.info("Created Cash Desk Account ID: " + transactionId);
                return transactionId;
            }
        } catch (Exception e) {
            LOGGER.warning("Failed to create cash account: " + e.getMessage());
        }
        return null;
    }

    /**
     * Closes the cash transaction with the provided transaction ID by executing a GraphQL mutation to change its status.
     *
     * @param transactionId The ID of the cash transaction to be closed. After execution, the transaction with the provided ID is closed.
     */
    public void closeCashTransaction(String transactionId) {

        try {
            Map<String, Object> variables = ChangeCashTransaction.closeCashTransaction(transactionId);

            String response = graphqlClient.executeQuery(
                    GraphQlQueries.CHANGE_CASH_TRANSACTION_STATUS,
                    variables,
                    ApiConstants.CASH_DESK_ENDPOINT, cashDeskHeaders);

            LOGGER.info("GraphQL Response: " + response);
        } catch (Exception e) {
            LOGGER.warning("Failed to close cash transaction with ID: " + transactionId);
        }
    }

    /**
     * Deletes the cash transaction with the provided transaction ID by executing a GraphQL mutation to change its status.
     *
     * @param transactionId The ID of the cash transaction to be deleted. After execution, the transaction with the provided ID is deleted.
     */
    public void deleteCashTransaction(String transactionId) {

        try {
            Map<String, Object> variables = ChangeCashTransaction.deleteCashTransaction(transactionId);

            String response = graphqlClient.executeQuery(
                    GraphQlQueries.CHANGE_CASH_TRANSACTION_STATUS,
                    variables,
                    ApiConstants.CASH_DESK_ENDPOINT, cashDeskHeaders);

            LOGGER.info("GraphQL Response: " + response);
        } catch (Exception e) {
            LOGGER.warning("Failed to switch language to: " + transactionId);
        }
    }

    /**
     * Extracts the assigned ID of a cash desk account from the GraphQL response.
     *
     * @param response The JSON response returned from the GraphQL query.
     * @return The assigned ID of the created cash desk account, or null if the ID is not found or an error occurs.
     */
    private String getCashAccountAssignedId(String response) {
        try {
            JsonNode jsonNode = new ObjectMapper().readTree(response);
            JsonNode assignedIdNode = jsonNode.path("data").path("createCashAccount").path("assignedId");

            if (!assignedIdNode.isMissingNode()) {
                return assignedIdNode.asText();
            } else {
                LOGGER.warning("assignedId not found in response!");
            }
        } catch (Exception e) {
            LOGGER.warning("Failed to extract assignedId - " + e.getMessage());
        }
        return null;
    }

    /**
     * Extracts the cash transaction ID from the GraphQL response.
     *
     * @param response The GraphQL response as a JSON string.
     * @return The cash transaction ID, or null if not found or extraction fails.
     */
    private String getCashTransactionId(String response) {
        try {
            JsonNode jsonNode = new ObjectMapper().readTree(response);
            JsonNode transactionIdNode = jsonNode.path("data").path("createCashTransactionV2").path("cashTransaction").path("id");

            if (!transactionIdNode.isMissingNode()) {
                return transactionIdNode.asText();
            } else {
                LOGGER.warning("transactionId not found in response");
            }
        } catch (Exception e) {
            LOGGER.warning("Failed to extract assignedId - " + e.getMessage());
        }
        return null;
    }
}
