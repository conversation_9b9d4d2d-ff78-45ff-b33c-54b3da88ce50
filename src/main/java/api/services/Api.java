package api.services;

import logger.ConsoleLogger;
import utils.GraphqlClient;
import utils.KeycloakAuth;

import java.util.logging.Logger;

public class Api {
    protected static final Logger LOGGER = ConsoleLogger.getLogger();
    private static final String PASSWORD = "Tester-123";

    private static Api instance;
    private final GraphqlClient graphqlClient;

    private Api(String username) {
        String accessToken = KeycloakAuth.authenticate(username, PASSWORD);
        this.graphqlClient = new GraphqlClient(accessToken);
    }

    /**
     * Returns a singleton instance of the Api class. If the instance does not already exist,
     * it is created using the provided username.
     *
     * @param username The username used for authentication.
     * @return The singleton instance of the Api class.
     */
    public static Api getInstance(String username) {
        if (instance == null) {
            instance = new Api(username);
        }
        return instance;
    }

    /**
     * Gets the GraphqlClient instance used by this Api object.
     *
     * @return the GraphqlClient instance
     */
    public GraphqlClient getGraphqlClient() {
        return this.graphqlClient;
    }
}
