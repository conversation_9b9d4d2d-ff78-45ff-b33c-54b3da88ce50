package api.services;

import api.apiConstants.ApiConstants;
import api.models.userSettingsModels.UserDetails;
import api.queries.GraphQlQueries;
import logger.ConsoleLogger;
import utils.GraphqlClient;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

public class UserSettingsService {

    protected static final Logger LOGGER = ConsoleLogger.getLogger();
    private final GraphqlClient graphqlClient;

    public UserSettingsService(Api api) {
        this.graphqlClient = api.getGraphqlClient();
    }

    /**
     * Updates the user's preferred language by executing a GraphQL mutation.
     *
     * @param companyId ID of the company of the user whose language is to be updated.
     * @param userId    ID of the user whose language is to be updated.
     * @param language  The new language to be set for the user.
     */
    public void setLanguage(String companyId, String userId, String language) {

        Map<String, String> headers = new HashMap<>();
        headers.put("selected-company", companyId);

        try {
            Map<String, Object> variables = UserDetails.setLanguage(userId, language);

            String response = graphqlClient.executeQuery(
                    GraphQlQueries.UPDATE_USER_DETAILS,
                    variables,
                    ApiConstants.MASTERDATA_ENDPOINT, headers);

            LOGGER.info("GraphQL Response: " + response);
        } catch (Exception e) {
            LOGGER.warning("Failed to switch language to: " + language + " - " + e.getMessage());
        }
    }
}
