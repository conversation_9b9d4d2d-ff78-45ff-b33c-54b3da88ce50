package constants;

import java.time.format.DateTimeFormatter;

public class ConstantsFile {

    public static final DateTimeFormatter DMY_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    public static final DateTimeFormatter ISO_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final String CREDENTIALS_FILE_PATH = "src/main/resources/testData/credentials.json";
    public static final String AUTOMATION_COMPANY = "Automation company ";
    public static final String APPIUM_COMPANY = "Appium company";
    public static final String COMPANY_IS_NOT_CHANGED = "Company is not changed";
    public static final String EMPTY_STRING = "";
    public static final String VALID_IBAN_I = "**********************";
    public static final String VALID_IBAN_II = "**********************";
    public static final String NL_IBAN = "******************";
    public static final String INVALID_IBAN = "IBAN";
    public static final String VALID_BIC_I = "ACTUALLY";
    public static final String VALID_BIC_II = "BYLADEM1WOR";
    public static final String VALID_GENERATED_BIC = "COBADEFFXXX";
    public static final String INVALID_BIC = "BIC";
}
