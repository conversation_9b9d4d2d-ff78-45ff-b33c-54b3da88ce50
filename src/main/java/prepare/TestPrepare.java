package prepare;

import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import pages.navigationPages.DashboardPage;
import pages.webPages.LandingPage;
import pages.webPages.LoginPage;
import util.TestUtilities;

import static driver.DriverManager.getDriver;


public class TestPrepare extends TestUtilities {
    @AndroidFindBy(accessibility = "top_app_bar_title")
    @iOSXCUITFindBy(accessibility = "NavigationController.Title")
    private static WebElement DASHBOARD_TAB;
    public void loginWithUser(String username, String password) {

        LandingPage landingPage = new LandingPage();
        LoginPage loginPage = landingPage.login();

        loginPage
                .setUsername(username)
                .setPassword(password)
                .signIn();
    }

    public void loginWithStandardUserWithAllPrivileges() {

        LandingPage landingPage = new LandingPage();
        LoginPage loginPage = landingPage.login();

        loginPage
                .setUsername(getUsername("STANDARD_USER_WITH_ALL_PRIVILEGES"))
                .setPassword(getPassword("STANDARD_USER_PASSWORD"))
                .signIn();
    }

    public void loginWithUserTest() {
        if (isAndroid()) {
            loginWithStandardUserWithAllPrivileges();
            return;
        }

        try {
            // Use try-catch to handle the case when element is not found
            WebElement dashboardTitle = getDriver().findElement(By.name("NavigationController.Title"));
            if (dashboardTitle.isDisplayed()) {
                System.out.println("[LOGIN GUARD] User already logged in, skipping login step.");
                return;
            }
        } catch (Exception e) {
            System.out.println("[LOGIN GUARD] User not logged in, proceeding with login.");
        }
        
        loginWithStandardUserWithAllPrivileges();
    }
}
