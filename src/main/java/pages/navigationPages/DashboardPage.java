package pages.navigationPages;

import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class DashboardPage extends HeaderAndFooterPage {

    @AndroidFindBy(accessibility = "top_app_bar_title")
    @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Dashboard']")
    private static WebElement DASHBOARD_TAB;

    //TODO - investigate, this method is unnecessary (verify something else)
    public DashboardPage verifyDashboardTabDisplayed() {
        assert isDisplayed(DASHBOARD_TAB);
        return this;
    }

    public DashboardPage verifyDashboardHeaderText(String expectedText) {
        verifyElementText(expectedText, DASHBOARD_TAB);
        return this;
    }

    public DashboardPage verifyDashboardHeader() {
        verifyHeaderText("DASHBOARD_HEADER");
        return this;
    }
}
