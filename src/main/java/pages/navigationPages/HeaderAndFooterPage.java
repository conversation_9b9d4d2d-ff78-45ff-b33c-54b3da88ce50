package pages.navigationPages;

import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;
import pages.uploadPages.ScanPage;
import pages.cashDeskPages.CashDeskPage;
import pages.documentsPages.DocumentsPage;
import pages.emailsPages.EmailsPage;
import pages.invoicesPages.InvoicesPage;
import pages.messagesPages.MessagesPage;
import pages.searchPages.SearchPage;
import util.Base;

public abstract class HeaderAndFooterPage extends Base {

    //HEADER

    @AndroidFindBy(accessibility = "Top App Bar Navigation Icon")
    @iOSXCUITFindBy(id = "MainTabBarController.Button.Menu")
    private static WebElement HUMBURGER_BTN;

    @AndroidFindBy(accessibility = "top_app_bar_title")
    @iOSXCUITFindBy(className = "XCUIElementTypeNavigationBar")
    private static WebElement TEXT_TAB;

    @AndroidFindBy(uiAutomator = "new UiSelector().className(\"android.widget.Button\").instance(1)")
    @iOSXCUITFindBy(accessibility = "Search")
    private static WebElement SEARCH_BTN;

    //BOTTOM BAR

    @AndroidFindBy(accessibility = "Dashboard")
    private static WebElement DASHBOARD_BTN;

    @AndroidFindBy(accessibility = "Messages")
    private static WebElement MESSAGES_BTN;

    @AndroidFindBy(accessibility = "Documents")
    private static WebElement DOCUMENTS_BTN;

    @AndroidFindBy(accessibility = "Invoices")
    private static WebElement INVOICES_BTN;

    @AndroidFindBy(accessibility = "Bank")
    private static WebElement BANK_BTN;

    @AndroidFindBy(accessibility = "Tasks")
    private static WebElement TASKS_BTN;

    @AndroidFindBy(accessibility = "Cash Desk")
    private static WebElement CASH_DESK_BTN;

    @AndroidFindBy(accessibility = "Emails")
    private static WebElement EMAILS_BTN;

    @AndroidFindBy(accessibility = "Add")
    private static WebElement SCAN_BTN;

    public MainMenuPage hamburgerBtn() {
        click(HUMBURGER_BTN);
        return new MainMenuPage();
    }

    public SearchPage searchBtn() {
        click(SEARCH_BTN);
        return new SearchPage();
    }

    protected DashboardPage dashboardBtn() {
        click(DASHBOARD_BTN);
        return new DashboardPage();
    }

    protected MessagesPage messagesBtn() {
        click(MESSAGES_BTN);
        return new MessagesPage();
    }

    protected DocumentsPage documentsBtn() {
        click(DOCUMENTS_BTN);
        return new DocumentsPage();
    }

    protected InvoicesPage invoicesBtn() {
        click(INVOICES_BTN);
        return new InvoicesPage();
    }

    protected CashDeskPage cashDeskBtn() {
        click(CASH_DESK_BTN);
        return new CashDeskPage();
    }

    protected EmailsPage emailsBtn() {
        click(EMAILS_BTN);
        return new EmailsPage();
    }

    protected ScanPage scanBtn() {
        click(SCAN_BTN);
        return new ScanPage();
    }

    public HeaderAndFooterPage verifyHamburgerBtnDisplayed() {
        isDisplayed(HUMBURGER_BTN);
        return this;
    }

    protected HeaderAndFooterPage verifyHeaderText(String expectedText) {
        verifyLocalizedElementText(expectedText, TEXT_TAB);
        return this;
    }

    protected HeaderAndFooterPage verifyDashboardBtnVisible() {
        isDisplayed(DASHBOARD_BTN);
        return this;
    }

    protected HeaderAndFooterPage verifyMessagesBtnVisible() {
        isDisplayed(MESSAGES_BTN);
        return this;
    }

    protected HeaderAndFooterPage verifyDocumentsBtnVisible() {
        isDisplayed(DOCUMENTS_BTN);
        return this;
    }

    protected HeaderAndFooterPage verifyInvoicesBtnVisible() {
        isDisplayed(INVOICES_BTN);
        return this;
    }

    protected HeaderAndFooterPage verifyBankBtnVisible() {
        isDisplayed(BANK_BTN);
        return this;
    }

    protected HeaderAndFooterPage verifyTasksBtnVisible() {
        isDisplayed(TASKS_BTN);
        return this;
    }

    protected HeaderAndFooterPage verifyCashDeskBtnVisible() {
        isDisplayed(CASH_DESK_BTN);
        return this;
    }

    protected HeaderAndFooterPage verifyEmailsBtnVisible() {
        isDisplayed(EMAILS_BTN);
        return this;
    }

    protected HeaderAndFooterPage verifyScanBtnVisible() {
        isDisplayed(SCAN_BTN);
        return this;
    }
}
