package pages.navigationPages;

import constants.ConstantsFile;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.WebElement;
import pages.bankPages.BankPage;
import pages.cashDeskPages.CashDeskPage;
import pages.documentsPages.DocumentsPage;
import pages.emailsPages.EmailsPage;
import pages.exportsPages.ExportsPage;
import pages.invoicesPages.InvoicesPage;
import pages.messagesPages.MessagesPage;
import pages.partnerMasterdataPages.PartnerMasterdataPage;
import pages.settingsPages.SettingsPage;
import pages.tasksPages.TasksPage;
import pages.webPages.LandingPage;
import pages.webPages.StaticPages;
import util.Base;

public class MainMenuPage extends Base {

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/logo")
    @iOSXCUITFindBy(accessibility = "")
    private static WebElement CAPITAIN_LOGO;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/usersName")
    @iOSXCUITFindBy(accessibility = "")
    private static WebElement USER_NAME;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/companyTitle")
    @iOSXCUITFindBy(accessibility = "SideMenuCell.TextField.Company")
    private static WebElement COMPANY_TITLE_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/switchCompanyTv")
    private static WebElement SWITCH_COMPANY_TEXT_HEADER_POP_UP_WINDOW;

    @iOSXCUITFindBy(className = "XCUIElementTypePickerSheel")
    private static WebElement SWITCH_COMPANY_PICKER;

    @AndroidFindBy(accessibility = "navigation_drawer_dashboard_button")
    @iOSXCUITFindBy(accessibility = "SideMenuVC.Button.Dashboard")
    private static WebElement DASHBOARD_BTN;

    @AndroidFindBy(accessibility = "navigation_drawer_messages_button")
    @iOSXCUITFindBy(accessibility = "SideMenuVC.Button.Messages")
    private static WebElement MESSAGES_BTN;

    @AndroidFindBy(accessibility = "navigation_drawer_bank_button")
    @iOSXCUITFindBy(accessibility = "SideMenuVC.Button.BankTransactions")
    private static WebElement BANK_BTN;

    @AndroidFindBy(accessibility = "navigation_drawer_invoices_button")
    @iOSXCUITFindBy(accessibility = "SideMenuVC.Button.Invoices")
    private static WebElement INVOICES_BTN;

    @AndroidFindBy(accessibility = "navigation_drawer_tasks_button")
    @iOSXCUITFindBy(accessibility = "Quests")
    private static WebElement TASKS_BTN;

    @AndroidFindBy(accessibility = "navigation_drawer_cash_desk_button")
    @iOSXCUITFindBy(accessibility = "SideMenuVC.Button.CashTransactions")
    private static WebElement CASH_DESK_BTN;

    @AndroidFindBy(accessibility = "navigation_drawer_emails_button")
    private static WebElement EMAIL_BTN;

    @AndroidFindBy(accessibility = "navigation_drawer_documents_button")
    @iOSXCUITFindBy(accessibility = "Documents")
    private static WebElement DOCUMENT_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/settingsLL")
    @iOSXCUITFindBy(accessibility = "Settings")
    private static WebElement SETTINGS_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/profileLL")
    @iOSXCUITFindBy(accessibility = "Partner masterdata")
    private static WebElement PARTNER_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/exportLL")//
    @iOSXCUITFindBy(accessibility = "Exports")
    private static WebElement EXPORT_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/arrowMenuIv")
    private static WebElement ARROW_MENU_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/faqLL")
    @iOSXCUITFindBy(accessibility = "F.A.Q.")
    private static WebElement FAQ_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/feedbackLL")
    @iOSXCUITFindBy(accessibility = "Send Feedback")
    private static WebElement SEND_FEEDBACK_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/textView2")
    private static WebElement SEND_FEEDBACK_DIALOG_TITLE;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/textView3")
    private static WebElement SEND_FEEDBACK_DIALOG_MESSAGE;

    @AndroidFindBy(id = "android:id/button2")
    @iOSXCUITFindBy(accessibility = "Cancel")
    private static WebElement SEND_FEEDBACK_CANCEL_BTN;

    @AndroidFindBy(id = "android:id/button1")
    @iOSXCUITFindBy(accessibility = "Compose Report")
    private static WebElement SEND_FEEDBACK_COMPOSE_REPORT_BTN;

    @AndroidFindBy(id = "com.google.android.gm:id/peoplekit_chip")
    private static WebElement SEND_FEEDBACK_EMAIL;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/logoutLL")
    @iOSXCUITFindBy(id = "Logout")
    private static WebElement LOGOUT_BTN;

    @AndroidFindBy(id = "android:id/alertTitle")
    @iOSXCUITFindBy(id = "Info")
    private static WebElement INFO_TITLE;

    @AndroidFindBy(id = "android:id/message")
    @iOSXCUITFindBy(id = "Are you sure you want to logout?")
    private static WebElement INFO_TEXT;

    @AndroidFindBy(id = "android:id/button1")
    @iOSXCUITFindBy(id = "SideMenuVC.LogoutAlert.Button.Logout")
    private static WebElement INFO_LOGOUT_BTN;

    @AndroidFindBy(id = "android:id/button2")
    @iOSXCUITFindBy(id = "SideMenuVC.LogoutAlert.Button.Cancel")
    private static WebElement INFO_CANCEL_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/imprintTv")
    private static WebElement IMPRINT_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/dataPrivacyTv")
    private static WebElement DATA_PRIVACY_BTN;

    @AndroidFindBy(uiAutomator = "new UiSelector().text(\"Automation company \")")
    private static WebElement AUTOMATION_COMPANY_NAME;

    @AndroidFindBy(uiAutomator = "new UiSelector().text(\"Appium company\")")
    private static WebElement APPIUM_COMPANY_NAME;

    public String getCompanyNameText() {
        return getText(COMPANY_TITLE_BTN);
    }

    public MainMenuPage openSwitchCompanyDialog() {
        click(COMPANY_TITLE_BTN);
        return this;
    }

    public MainMenuPage switchCompany(String currentCompany) {
        if (currentCompany.equals(ConstantsFile.AUTOMATION_COMPANY)) {
            click(APPIUM_COMPANY_NAME);
        } else if (currentCompany.equals(ConstantsFile.APPIUM_COMPANY)) {
            click(AUTOMATION_COMPANY_NAME);
        }
        return new MainMenuPage();
    }

    public MainMenuPage dashboardBtn() {
        click(DASHBOARD_BTN);
        return this;
    }

    public DocumentsPage documentsBtn() {
        click(DOCUMENT_BTN);
        return new DocumentsPage();
    }

    public MessagesPage messagesBtn() {
        click(MESSAGES_BTN);
        return new MessagesPage();
    }

    public InvoicesPage invoicesBtn() {
        click(INVOICES_BTN);
        return new InvoicesPage();
    }

    public BankPage bankBtn() {
        click(BANK_BTN);
        return new BankPage();
    }

    public TasksPage tasksBtn() {
        click(TASKS_BTN);
        return new TasksPage();
    }

    public CashDeskPage cashDeskBtn() {
        click(CASH_DESK_BTN);
        return new CashDeskPage();
    }

    public EmailsPage emailsBtn() {
        click(EMAIL_BTN);
        return new EmailsPage();
    }

    public SettingsPage settingsBtn() {
        click(SETTINGS_BTN);
        return new SettingsPage();
    }

    public PartnerMasterdataPage partnerBtn() {
        click(PARTNER_BTN);
        return new PartnerMasterdataPage();
    }

    public ExportsPage exportsBtn() {
        click(EXPORT_BTN);
        return new ExportsPage();
    }

    public MainMenuPage expandArrowBtn() {
        click(ARROW_MENU_BTN);
        return this;
    }

    public MainMenuPage expandArrowBtnDisplayed() {
        assert isDisplayed(ARROW_MENU_BTN);
        return this;
    }

    public StaticPages clickOnFAQBtn() {
        click(FAQ_BTN);
        return new StaticPages();
    }

    public MainMenuPage sendFeedback() {
        click(SEND_FEEDBACK_BTN);
        return this;
    }

    public MainMenuPage sendFeedbackCancelBtn() {
        click(SEND_FEEDBACK_CANCEL_BTN);
        return this;
    }

    public MainMenuPage sendFeedbackComposeReportBtn() {
        click(SEND_FEEDBACK_COMPOSE_REPORT_BTN);
        return this;
    }

    public MainMenuPage logout() {
        click(LOGOUT_BTN);
        return this;
    }

    public LandingPage logoutOnInfoDialog() {
        click(INFO_LOGOUT_BTN);
        return new LandingPage();
    }

    public MainMenuPage cancelOnInfoDialog() {
        click(INFO_CANCEL_BTN);
        return this;
    }

    public StaticPages imprintBtn() {
        click(IMPRINT_BTN);
        return new StaticPages();
    }

    public StaticPages dataPrivacyBtn() {
        click(DATA_PRIVACY_BTN);
        return new StaticPages();
    }

    public MainMenuPage verifyCapitainLogoVisible() {
        isDisplayed(CAPITAIN_LOGO);
        return this;
    }

    public MainMenuPage verifyUserNameVisible() {
        isDisplayed(USER_NAME);
        return this;
    }

    public MainMenuPage verifyCompanyNameSwitched(String previousCompanyName, String newCompanyName) {
        Assertions.assertNotEquals(previousCompanyName, newCompanyName, ConstantsFile.COMPANY_IS_NOT_CHANGED);
        return this;
    }

    public MainMenuPage verifySendFeedbackDialogTitle() {
        verifyLocalizedElementText("SEND_FEEDBACK_DIALOG_TITLE", SEND_FEEDBACK_DIALOG_TITLE);
        return this;
    }

    public MainMenuPage verifySendFeedbackDialogMessage() {
        verifyLocalizedElementText("SEND_FEEDBACK_DIALOG_MESSAGE", SEND_FEEDBACK_DIALOG_MESSAGE);
        return this;
    }

    public MainMenuPage verifySendFeedbackCancelBtnVisible() {
        assert isDisplayed(SEND_FEEDBACK_CANCEL_BTN);
        return this;
    }

    public MainMenuPage verifySendFeedbackComposeReportBtnVisible() {
        assert isDisplayed(SEND_FEEDBACK_COMPOSE_REPORT_BTN);
        return this;
    }

    public MainMenuPage verifySendFeedBackEmailOpened() {
        assert isDisplayed(SEND_FEEDBACK_EMAIL);
        return this;
    }

    public MainMenuPage verifyCapitainEmailIsPredifined() {
        String actualText = getText(SEND_FEEDBACK_EMAIL);
        Assertions.assertEquals("<EMAIL>", actualText);
        return this;
    }

//    public MainMenuPage verifyLogoutButtonText(String expectedText) {
//        String actualText = getText(LOGOUT_BTN);
//        verifyLocalizedElementText("");
//        return this;
//    }

    public MainMenuPage verifyLogoutBtnVisible() {
        assert isDisplayed(LOGOUT_BTN);
        return this;
    }

    public MainMenuPage verifyInfoDialogMessage() {
        verifyLocalizedElementText("INFO_TEXT", INFO_TEXT);
        return this;
    }

    public MainMenuPage verifyInfoDialogTitle() {
        verifyLocalizedElementText("INFO_TITLE", INFO_TITLE);
        return this;
    }

    public MainMenuPage verifyInfoDialogLogoutBtnText() {
        verifyLocalizedElementText("INFO_LOGOUT_BTN", INFO_LOGOUT_BTN);
        return this;
    }

    public MainMenuPage verifyInfoDialogCancelBtnText() {
        verifyLocalizedElementText("INFO_CANCEL_BTN", INFO_CANCEL_BTN);
        return this;
    }
}
