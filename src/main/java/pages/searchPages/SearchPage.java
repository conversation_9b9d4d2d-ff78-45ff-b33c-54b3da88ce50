package pages.searchPages;

import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;
import util.Base;

public class SearchPage extends Base {

    @AndroidFindBy(uiAutomator = "new UiSelector().text(\"Back\")")
    @iOSXCUITFindBy(accessibility = "")
    private static WebElement BACK_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/searchEditText")
    @iOSXCUITFindBy(accessibility = "")
    private static WebElement SEARCH_INPUT_FIELD;

    public SearchPage verifyBackButtonVisible() {
       isDisplayed(BACK_BTN);
       return this;
    }

    public SearchPage verifySearchInputFieldVisible() {
        isDisplayed(SEARCH_INPUT_FIELD);
        return this;
    }
}
