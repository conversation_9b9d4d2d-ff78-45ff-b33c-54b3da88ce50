package pages.cashDeskPages;

import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;
import util.Base;

public class CloseAllCashTransactionsDialog extends Base {

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/title")
    public WebElement DIALOG_TITLE;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/message")
    public WebElement DIALOG_MESSAGE;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/negativeButton")
    public WebElement CANCEL_BUTTON;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/positiveButton")
    public WebElement CLOSE_BUTTON;

    public CashDeskPage cancelBtn() {
        click(CANCEL_BUTTON);
        return new CashDeskPage();
    }

    public CashDeskPage closeBtn() {
        click(CLOSE_BUTTON);
        return new CashDeskPage();
    }
}
