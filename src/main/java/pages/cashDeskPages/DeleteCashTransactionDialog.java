package pages.cashDeskPages;

import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;
import util.Base;

public class DeleteCashTransactionDialog extends Base {

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/title")
    private static WebElement DIALOG_TITLE;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/message")
    private static WebElement DIALOG_MESSAGE;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/negativeButton")
    private static WebElement CANCEL_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/positiveButton")
    private static WebElement DELETE_BTN;

    public AddEditCashTransactionPage tapCancel_btn() {
        click(CANCEL_BTN);
        return new AddEditCashTransactionPage();
    }

    public CashDeskPage tapDelete_btn() {
        click(DELETE_BTN);
        return new CashDeskPage();
    }

    public  DeleteCashTransactionDialog verifyDialogTitle(String dialogTitle) {
        verifyLocalizedElementText(dialogTitle, DIALOG_TITLE);
        return this;
    }

    public  DeleteCashTransactionDialog verifyDialogMessage(String dialogMessage) {
        verifyLocalizedElementText(dialogMessage, DIALOG_MESSAGE);
        return this;
    }
}
