package pages.cashDeskPages;

import io.appium.java_client.pagefactory.AndroidBy;
import io.appium.java_client.pagefactory.AndroidFindAll;
import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;
import util.Base;

import java.util.List;

public class AddEditCashTransactionPage extends Base {

    @AndroidFindBy(accessibility = "number_sequence")
    private static WebElement SEQUENCE_NUMBER;

    @AndroidFindBy(accessibility = "close_or_delete_button")
    private static WebElement EXIT_BTN;

    @AndroidFindBy(accessibility = "close_or_delete_button")
    private static WebElement DELETE_BTN;

    @AndroidFindBy(accessibility = "save_button")
    private static WebElement SAVE_BTN;

    @AndroidFindBy(accessibility = "cash_desk_add_or_edit_dropdown_picker")
    private static WebElement CASH_DESK_NAME_DROPDOWN;

    @AndroidFindAll(@AndroidBy(accessibility = "cash_desk_add_or_edit_dropdown_picker_item"))
    private List<WebElement> CASH_DESK_NAME_DROPDOWN_ITEMS;

    @AndroidFindBy(uiAutomator = "new UiSelector().className(\"android.view.View\").instance(9)")
    private static WebElement PAYMENT_DATE_PICKER;

    @AndroidFindBy(uiAutomator = "new UiSelector().className(\"android.widget.EditText\").instance(2)")
    private static WebElement COUNTERPART_INPUT;

    @AndroidFindBy(uiAutomator = "new UiSelector().className(\"android.widget.EditText\").instance(3)")
    private static WebElement PAYMENT_DETAILS_INPUT;

    @AndroidFindBy(uiAutomator = "new UiSelector().className(\"android.widget.EditText\").instance(4)")
    private static WebElement AMOUNT_INPUT;

    @AndroidFindBy(accessibility = "cost_center_dropdown_picker")
    private static WebElement COST_CENTER_DROPDOWN;

    @AndroidFindBy(accessibility = "add_subtransaction_button")
    private static WebElement ADD_SUB_TRANSACTION_BTN;

    //TODO - Add sub-transaction elements here

    @AndroidFindBy(accessibility = "summary_icon_arrow_down")
    private static WebElement EXPAND_COLAPSE_BALANCES_BTN;

    public CashDeskPage tapExitBtn() {
        click(EXIT_BTN);
        return new CashDeskPage();
    }

    public DeleteCashTransactionDialog tapDeleteBtn() {
        click(DELETE_BTN);
        return new DeleteCashTransactionDialog();
    }

    public CashDeskPage tapSaveBtn() {
        click(SAVE_BTN);
        return new CashDeskPage();
    }

    public AddEditCashTransactionPage setPaymentDate(String paymentDate) {
        click(PAYMENT_DATE_PICKER);

        SelectDateDialog selectDateDialog = new SelectDateDialog();
        selectDateDialog.clickCustomDateBtn()
                .enterDate(paymentDate)
                .clickConfirmBtn();
        return this;
    }

    public AddEditCashTransactionPage setCounterpart(String counterpart) {
        clearAndType(COUNTERPART_INPUT, counterpart);
        return this;
    }

    public AddEditCashTransactionPage setPaymentDetails(String paymentDetails) {
        clearAndType(PAYMENT_DETAILS_INPUT, paymentDetails);
        return this;
    }

    public AddEditCashTransactionPage setAmount(String amount) {
        clearAndType(AMOUNT_INPUT, amount);
        return this;
    }

    //TODO - Add set cost center method here

    public AddEditCashTransactionPage tapAddSubTransactionBtn() {
        click(ADD_SUB_TRANSACTION_BTN);
        return this;
    }

    //TODO - Add sub-transaction methods here

    public AddEditCashTransactionPage tapExpandCollapseBalancesBtn() {
        click(EXPAND_COLAPSE_BALANCES_BTN);
        return this;
    }

    public AddEditCashTransactionPage verifySequenceNumber(String sequenceNumber) {
        verifyElementText(sequenceNumber, SEQUENCE_NUMBER);
        return this;
    }

    public AddEditCashTransactionPage verifyCashDeskName(String cashDeskName) {
        verifyElementText(cashDeskName, CASH_DESK_NAME_DROPDOWN);
        return this;
    }

    public AddEditCashTransactionPage verifyPaymentDete(String paymentDetails) {
        verifyElementText(paymentDetails, PAYMENT_DATE_PICKER);
        return this;
    }

    public AddEditCashTransactionPage verifyCounterpart(String counterpart) {
        verifyElementText(counterpart, COUNTERPART_INPUT);
        return this;
    }

    public AddEditCashTransactionPage verifyPaymentDetails(String paymentDetails) {
        verifyElementText(paymentDetails, PAYMENT_DETAILS_INPUT);
        return this;
    }

    public AddEditCashTransactionPage verifyAmount(String amount) {
        verifyElementText(amount, AMOUNT_INPUT);
        return this;
    }

    public AddEditCashTransactionPage verifyCostCenter(String costCenter) {
        verifyElementText(costCenter, COST_CENTER_DROPDOWN);
        return this;
    }
}
