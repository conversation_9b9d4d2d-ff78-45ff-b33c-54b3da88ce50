package pages.cashDeskPages;

import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;
import util.Base;

public class SelectDateDialog extends Base {

    //TODO - new class has been created for Android (This probably need debug)
    @AndroidFindBy(uiAutomator = "new UiSelector().className(\"android.widget.Button\").instance(0)")
    public WebElement CUSTOM_DATE_BTN;

    @AndroidFindBy(className = "android.widget.EditText")
    public WebElement DATE_INPUT;

    @AndroidFindBy(uiAutomator = "new UiSelector().className(\"android.widget.Button\").instance(1)")
    public WebElement CONFIRM_BTN;

    public SelectDateDialog clickCustomDateBtn() {
        click(CUSTOM_DATE_BTN);
        return this;
    }

    public SelectDateDialog enterDate(String date) {
        clearAndType(DATE_INPUT, date);
        return this;
    }

    public AddEditCashTransactionPage clickConfirmBtn() {
        click(CONFIRM_BTN);
        return new AddEditCashTransactionPage();
    }
}
