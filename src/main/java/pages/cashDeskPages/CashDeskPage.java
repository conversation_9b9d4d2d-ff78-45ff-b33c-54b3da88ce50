package pages.cashDeskPages;

import io.appium.java_client.pagefactory.*;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import pages.navigationPages.HeaderAndFooterPage;

import java.util.List;

public class CashDeskPage extends HeaderAndFooterPage {

    @AndroidFindBy(uiAutomator = "new UiSelector().resourceId(\"input\")") //TODO there are added accessibility id on some builds
    private static WebElement CASH_ACCOUNTS_DROPDOWN;

    @AndroidFindAll(@AndroidBy(accessibility = "dropdown_item"))
    private List<WebElement> CASH_ACCOUNTS_DROPDOWN_ITEMS;

    @AndroidFindBy(accessibility = "showing_label")
    private static WebElement SHOWING;

    @AndroidFindBy(accessibility = "showing_size")
    private static WebElement NUMBER_OF_SHOWING_ITEMS;

    @AndroidFindBy(accessibility = "cash_desk_add_button")
    @iOSXCUITFindBy(accessibility = "")
    private static WebElement ADD_BTN;

    @AndroidFindBy(accessibility = "cash_desk_info_button")
    private static WebElement BALANCES_BTN;

    @AndroidFindBy(uiAutomator = "new UiSelector().className(\"android.widget.Button\").instance(4)") //TODO there are added accessibility id on some builds
    private static WebElement CLOSE_ALL_CASH_TRANSACTIONS_BTN;

    @AndroidFindBy(accessibility = "cash_desk_sort_button")
    private static WebElement CASH_DESK_SORT_BTN;

    @AndroidFindBy(accessibility = "cash_desk_filter_button")
    private static WebElement CASH_DESK_FILTER_BTN;

    @AndroidFindAll(@AndroidBy(accessibility = "list_item"))
    private List<WebElement> LIST_ITEMS;

    @AndroidFindAll(@AndroidBy(accessibility = "list_item_day"))
    private List<WebElement> LIST_ITEM_DATES;

    @AndroidFindAll(@AndroidBy(accessibility = "list_item_month"))
    private List<WebElement> LIST_ITEM_MONTHS;

    @AndroidFindAll(@AndroidBy(accessibility = "list_item_amount"))
    private List<WebElement> LIST_ITEM_AMOUNTS;

    @AndroidFindAll(@AndroidBy(accessibility = "list_item_statusIcon"))
    private List<WebElement> LIST_ITEM_STATUS_ICONS;

    public CashDeskPage selectCashAccount(String accountText) {
        click(CASH_ACCOUNTS_DROPDOWN);
        String xpath = String.format("//android.widget.TextView[@text='%s']", accountText);
        WebElement item = getDriver().findElement(By.xpath(xpath));
        item.click();
        return this;
    }

    public AddEditCashTransactionPage addCashTransaction() {
        click(ADD_BTN);
        return new AddEditCashTransactionPage();
    }

    public BalancesPage balancesBtn() {
        click(BALANCES_BTN);
        return new BalancesPage();
    }

    public CloseAllCashTransactionsDialog closeAllCashTransactionsBtn() {
        click(CLOSE_ALL_CASH_TRANSACTIONS_BTN);
        return new CloseAllCashTransactionsDialog();
    }

    public CashDeskPage sortBtn() {
        click(CASH_DESK_SORT_BTN);
        return this;
    }

    public CashDeskFiltersPage filterBtn() {
        click(CASH_DESK_FILTER_BTN);
        return new CashDeskFiltersPage();
    }

    public CashDeskPage verifyCashDeskHeader() {
        verifyHeaderText("CASH_DESK_HEADER");
        return this;
    }

    public CashDeskPage verifyNumberOfCashAccounts(int numberOfItems) {
        verifyNumberOfListItems(CASH_ACCOUNTS_DROPDOWN_ITEMS, numberOfItems);
        return this;
    }
}
