package pages.invoicesPages;

import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;
import util.Base;

public class InvoicesFiltersPage extends Base {

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/buttonStatus")
    private static WebElement STATUS_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/cbxStatusPay")
    private static WebElement PAYABLE_STATUS_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/buttonApplyFilter")
    private static WebElement APPLY_FILTER_BTN;

    public InvoicesFiltersPage statusBtn() {
        click(STATUS_BTN);
        return this;
    }

    public InvoicesFiltersPage payableStatusBtn() {
        click(PAYABLE_STATUS_BTN);
        return this;
    }

    public InvoicesPage applyFilterBtn() {
        click(APPLY_FILTER_BTN);
        return new InvoicesPage();
    }
}
