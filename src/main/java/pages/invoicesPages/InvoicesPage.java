package pages.invoicesPages;

import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;
import pages.navigationPages.HeaderAndFooterPage;
import pages.paymentPages.PaymentPage;

public class InvoicesPage extends HeaderAndFooterPage {

    @AndroidFindBy(accessibility = "invoice_invoices_filter_icon")
    private static WebElement FILTER_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/iconStatusImage")
    private static WebElement PAYABLE_ICON;

    @AndroidFindBy(uiAutomator = "new UiSelector().resourceId(\"app.capitain.io.qaOpen:id/iconStatusImage\").instance(0)")
    private static WebElement CLICK_FIRST_PAYABLE_ICON_IN_LIST;

    @AndroidFindBy(uiAutomator = "new UiSelector().resourceId(\"app.capitain.io.qaOpen:id/iconStatusImage\").instance(1)")
    private static WebElement CLICK_SECOND_PAYABLE_ICON_IN_LIST;

    public InvoicesFiltersPage filtersBtn() {
        click(FILTER_BTN);
        return new InvoicesFiltersPage();
    }

    public PaymentPage clickFirstPayableIconInList() {
        click(CLICK_FIRST_PAYABLE_ICON_IN_LIST);
        return new PaymentPage();
    }

    public PaymentPage clickSecondPayableIconInList() {
        click(CLICK_SECOND_PAYABLE_ICON_IN_LIST);
        return new PaymentPage();
    }

    public InvoicesPage verifyInvoicesHeader() {
        verifyHeaderText("INVOICES_HEADER");
        return this;
    }

    public InvoicesPage verifyPayableIcon() {
        isDisplayed(PAYABLE_ICON);
        return this;
    }
}
