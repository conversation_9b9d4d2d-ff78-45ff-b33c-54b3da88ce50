package pages.settingsPages;

import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;
import pages.navigationPages.DashboardPage;
import pages.navigationPages.HeaderAndFooterPage;

public class SettingsPage extends HeaderAndFooterPage {

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/profileSettingsTab")
    @iOSXCUITFindBy(id = "User")
    private static WebElement USER_TAB;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/languageInput")
    @iOSXCUITFindBy(id = "SettingsVC.Tab.User.Language")
    private static WebElement CHOOSE_LANGUAGE;

    @AndroidFindBy(xpath = "/hierarchy/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ListView/android.widget.CheckedTextView[2]")
    @iOSXCUITFindBy(xpath = "//XCUIElementTypeApplication[@name=\"Capitain Test\"]/XCUIElementTypeWindow[4]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypePicker/XCUIElementTypePickerWheel/XCUIElementTypeOther[3]")
    private static WebElement LANGUAGE_LIST_GERMAN;

    @AndroidFindBy(xpath = "/hierarchy/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ListView/android.widget.CheckedTextView[1]")
    @iOSXCUITFindBy(xpath = "//XCUIElementTypeApplication[@name=\"Capitain Test\"]/XCUIElementTypeWindow[4]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypePicker/XCUIElementTypePickerWheel/XCUIElementTypeOther[1]")
    private static WebElement LANGUAGE_LIST_ENGLISH;

    @AndroidFindBy(xpath = "//android.widget.CheckedTextView[@resource-id=\"android:id/text1\" and @text=\"Czech\"]")
    @iOSXCUITFindBy()
    private static WebElement LANGUAGE_LIST_CZECH;

    @AndroidFindBy(xpath = "//android.widget.CheckedTextView[@resource-id=\"android:id/text1\" and @text=\"Spanish\"]")
    @iOSXCUITFindBy()
    private static WebElement LANGUAGE_LIST_SPANISH;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/saveFl")
    @iOSXCUITFindBy(id = "Save")
    private static WebElement SAVE_BTN;

    @AndroidFindBy(id = "android:id/button1")
    @iOSXCUITFindBy(id = "")
    private static WebElement CONFIRM_CHANGING_LANGUAGE;

    public DashboardPage backToDashboard() {
        back();
        return new DashboardPage();
    }

    public SettingsPage userTab() {
        click(USER_TAB);
        return this;
    }

    public SettingsPage chooseLanguage() {
        click(CHOOSE_LANGUAGE);
        return this;
    }

    public SettingsPage lngListDe() {
        click(LANGUAGE_LIST_GERMAN);
        return this;
    }

    public SettingsPage lngListEn() {
        click(LANGUAGE_LIST_ENGLISH);
        return this;
    }

    public SettingsPage lngListCz() {
        click(LANGUAGE_LIST_CZECH);
        return this;
    }

    public SettingsPage lngListEs() {
        click(LANGUAGE_LIST_SPANISH);
        return this;
    }

    public SettingsPage saveBtn() {
        click(SAVE_BTN);
        return this;
    }

    public DashboardPage confirmChangingLanguage() {
        click(CONFIRM_CHANGING_LANGUAGE);
        return new DashboardPage();
    }

    public SettingsPage verifySettingsHeader() {
        verifyHeaderText("SETTINGS_HEADER");
        return this;
    }
}
