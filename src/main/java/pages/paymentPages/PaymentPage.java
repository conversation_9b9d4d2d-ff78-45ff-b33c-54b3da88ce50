package pages.paymentPages;

import constants.Constants;
import constants.ConstantsFile;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import pages.bankPages.BankAccountsSearchPage;
import util.Base;

public class PaymentPage extends Base {

    @AndroidFindBy(accessibility = "accountNameInput_input")
    private static WebElement ACCOUNT_FIELD;

    @AndroidFindBy(accessibility = "amountInput_input")
    private static WebElement AMOUNT_FIELD;

    @AndroidFindBy(accessibility = "amountInput_input")
    private static WebElement AMOUNT_TITLE_TXT;

    @AndroidFindBy(accessibility = "amountInput_error")
    private static WebElement AMOUNT_IS_REQUIRED_ERROR_MESSAGE;

    @AndroidFindBy(accessibility = "currencyInput_input")
    private static WebElement CURRENCY_FIELD;

    @AndroidFindBy(accessibility = "dateInput_input")
    private static WebElement DUE_DATE_FIELD;

    @AndroidFindBy(accessibility = "counterpartIbanInput_input")
    private static WebElement IBAN_FIELD;

    @AndroidFindBy(accessibility = "counterpartIbanInput_error")
    private static WebElement IBAN_IS_REQUIRED_ERROR_MESSAGE;

    @AndroidFindBy(accessibility = "counterpartBicInput_input")
    @iOSXCUITFindBy(accessibility = "")
    private static WebElement BIC_FIELD;

    @AndroidFindBy(accessibility = "counterpartBicInput_error")
    private static WebElement BIC_IS_REQUIRED_ERROR_MESSAGE;

    @AndroidFindBy(accessibility = "counterpartNameInput_input")
    private static WebElement COUNTERPART_NAME_FIELD;

    @AndroidFindBy(accessibility = "counterpartNameInput_error")
    private static WebElement COUNTERPART_NAME_IS_REQUIRED_ERROR_MESSAGE;

    @AndroidFindBy(accessibility = "purposeInput_input")
    private static WebElement PURPOSE_FIELD;

    @AndroidFindBy(accessibility = "purposeInput_error")
    private static WebElement PURPOSE_IS_REQUIRED_ERROR_MESSAGE;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/linkAccountButton")
    private static WebElement LINK_BANK_ACCOUNT_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/placeholderLayout")
    private static WebElement NOT_LINKED_BANK_TXT;

    @AndroidFindBy(accessibility = "invoice_payment_pay_button")
    @iOSXCUITFindBy(accessibility = "")
    private static WebElement PAY_BTN;

    public PaymentPage verifyNotLinkedMessageTxt() {
        WebElement noBankTextElementChild = NOT_LINKED_BANK_TXT.findElement(By.className("android.widget.TextView"));
        verifyLocalizedElementText("NOT_LINKED_BANK_TXT", noBankTextElementChild);
        return this;
    }

    public PaymentPage verifyAmountField() {
        isDisplayed(AMOUNT_FIELD);
        return this;
    }

    public PaymentPage verifyPayBtnDisplayed() {
        isDisplayed(PAY_BTN);
        return this;
    }

    public PaymentPage clearAmountField() {
        clear(AMOUNT_FIELD);
        return this;
    }

    public PaymentPage clearIbanField() {
        clear(IBAN_FIELD);
        return this;
    }

    public AddCounterpartIbanAndBicDialog clickIbanField() {
        click(IBAN_FIELD);
        return new AddCounterpartIbanAndBicDialog();
    }

    public PaymentPage verifyIbanValue(String iban) {
        verifyElementText(iban, IBAN_FIELD);
        return this;
    }

    public PaymentPage clearBicField() {
        clear(BIC_FIELD);
        return this;
    }

    public AddCounterpartIbanAndBicDialog clickBicField() {
        click(BIC_FIELD);
        return new AddCounterpartIbanAndBicDialog();
    }

    public PaymentPage verifyBicValue(String iban) {
        verifyElementText(iban, BIC_FIELD);
        return this;
    }

    public PaymentPage verifyEmptyBicField() {
        verifyElementText(ConstantsFile.EMPTY_STRING, BIC_FIELD);
        return this;
    }

    public PaymentPage clearCounterpartNameField() {
        clear(COUNTERPART_NAME_FIELD);
        return this;
    }

    public PaymentPage clearPurposeField() {
        clear(PURPOSE_FIELD);
        return this;
    }

    public PaymentPage setPurposeField(String text) {
        clear(PURPOSE_FIELD);
        type(PURPOSE_FIELD, text);
        return this;
    }

    public PaymentPage verifyPurposeField(String expectedText) {
        String actualText = getText(PURPOSE_FIELD);
        Assertions.assertEquals(expectedText, actualText, Constants.TEXT_DOES_NOT_MATCH);
        return this;
    }

    public PaymentPage verifyAmountRequiredErrorMessage() {
        verifyLocalizedElementText("AMOUNT_IS_REQUIRED_TXT", AMOUNT_IS_REQUIRED_ERROR_MESSAGE);
        return this;
    }

    public PaymentPage verifyIbanRequiredErrorMessage() {
        verifyLocalizedElementText("IBAN_IS_REQUIRED_TXT", IBAN_IS_REQUIRED_ERROR_MESSAGE);
        return this;
    }

    public PaymentPage verifyBicRequiredErrorMessage() {
        verifyLocalizedElementText("BIC_IS_REQUIRED_TXT", BIC_IS_REQUIRED_ERROR_MESSAGE);
        return this;
    }

    public PaymentPage verifyCounterpartNameRequiredErrorMessage() {
        verifyLocalizedElementText("COUNTERPART_NAME_IS_REQUIRED_TXT", COUNTERPART_NAME_IS_REQUIRED_ERROR_MESSAGE);
        return this;
    }

    public PaymentPage verifyPurposeRequiredErrorMessage() {
        verifyLocalizedElementText("PURPOSE_IS_REQUIRED_TXT", PURPOSE_IS_REQUIRED_ERROR_MESSAGE);
        return this;
    }

    public PaymentPage verifyLinkBankAccountBtnIsDisabled() {
        isDisabled(LINK_BANK_ACCOUNT_BTN);
        return this;
    }

    public BankAccountsSearchPage clickLinkBankAccountBtn() {
        click(LINK_BANK_ACCOUNT_BTN);
        return new BankAccountsSearchPage();
    }

    public PaymentPage verifyLinkBankAccountBtnIsEnabled() {
        isEnabled(LINK_BANK_ACCOUNT_BTN);
        return this;
    }

    public PaymentPage clickPayBtnAndStay() {
        if (!isAndroid()) {
            if (isIos()) {
                click(PAY_BTN);
            }
        }
        return this;
    }
}
