package pages.paymentPages;

import constants.Constants;
import io.appium.java_client.pagefactory.AndroidFindBy;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.WebElement;
import util.Base;

import static constants.Localization.getLocalizationText;

public class AddCounterpartIbanAndBicDialog extends Base {
    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/titleTextView")
    private static WebElement COUNTERPART_IBAN_AND_BIC_DIALOG_TITLE;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/inputEditText")
    private static WebElement INPUT_FIELD;

    @AndroidFindBy(id = "android:id/text1")
    private static WebElement NEW_VALUE_LABEL;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/errorText")
    private static WebElement INVALID_IBAN_BIC_ERROR_MESSAGE;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/saveButton")
    private static WebElement SAVE_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/closeButton")
    private static WebElement CLOSE_BTN;

    public AddCounterpartIbanAndBicDialog setIbanOrBic(String ibanOrBic) {
        clear(INPUT_FIELD);
        type(INPUT_FIELD, ibanOrBic);
        return this;
    }

    public AddCounterpartIbanAndBicDialog selectNewValue() {
        click(NEW_VALUE_LABEL);
        return this;
    }

    public AddCounterpartIbanAndBicDialog verifyIbanDialogTitleText() {
        verifyLocalizedElementText("IBAN_TITLE_NAME_DIALOG_TXT", COUNTERPART_IBAN_AND_BIC_DIALOG_TITLE);
        return this;
    }

    public AddCounterpartIbanAndBicDialog verifyBicDialogTitleText() {
        verifyLocalizedElementText("BIC_TITLE_DIALOG_TXT", COUNTERPART_IBAN_AND_BIC_DIALOG_TITLE);
        return this;
    }

    public AddCounterpartIbanAndBicDialog verifyNewIbanValueText(String ibanValue) {
        String expectedLocalizedText = getLocalizationText("NEW_IBAN_TXT");
        String actualText = getText(NEW_VALUE_LABEL);
        Assertions.assertEquals(expectedLocalizedText.concat(" ").concat(ibanValue), actualText, Constants.TEXT_DOES_NOT_MATCH);
        return this;
    }

    public AddCounterpartIbanAndBicDialog verifyNewBicValueText(String text) {
        String expectedLocalizedText = getLocalizationText("NEW_BIC_TXT");
        String actualText = getText(NEW_VALUE_LABEL);
        Assertions.assertEquals(expectedLocalizedText.concat(" ").concat(text), actualText, Constants.TEXT_DOES_NOT_MATCH);
        return this;
    }

    public AddCounterpartIbanAndBicDialog verifySaveButtonText() {
        verifyLocalizedElementText("SAVE_BTN_TXT", SAVE_BTN);
        return this;
    }

    public AddCounterpartIbanAndBicDialog verifyCloseButtonText() {
        verifyLocalizedElementText("CLOSE_BTN_TXT", CLOSE_BTN);
        return this;
    }

    public AddCounterpartIbanAndBicDialog verifySelectedNewValue(String ibanOrBic) {
        verifyElementText(ibanOrBic, INPUT_FIELD);
        return this;
    }

    public AddCounterpartIbanAndBicDialog verifyInvalidIbanErrorMessage() {
        verifyLocalizedElementText("INVALID_IBAN_ERROR_TXT", INVALID_IBAN_BIC_ERROR_MESSAGE);
        return this;
    }

    public AddCounterpartIbanAndBicDialog verifyInvalidBicErrorMessage() {
        verifyLocalizedElementText("INVALID_BIC_ERROR_TXT", INVALID_IBAN_BIC_ERROR_MESSAGE);
        return this;
    }

    public AddCounterpartIbanAndBicDialog verifySaveBtnDisabled() {
        isDisabled(SAVE_BTN);
        return this;
    }

    public PaymentPage clickSaveButton() {
        click(SAVE_BTN);
        return new PaymentPage();
    }

    public AddCounterpartIbanAndBicDialog clickCloseButton() {
        click(CLOSE_BTN);
        return this;
    }
}
