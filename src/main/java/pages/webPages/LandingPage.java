package pages.webPages;

import commands.Actions;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class LandingPage extends Actions {

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/instructionTextView")
    @iOSXCUITFindBy(id = "Capitain will log you in through Eurodata, please tap button to continue.")
    private static WebElement LOGIN_TEXT;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/loginButton")
    @iOSXCUITFindBy(id = "LoginViewController.Button.LogIn")
    private static WebElement LOGIN_BTN;

    @AndroidFindBy(id = "app.capitain.io.qaOpen:id/forgotPassword")
    @iOSXCUITFindBy(id = "Forgot Password?")
    private static WebElement FORGOT_PASSWORD;

    @AndroidFindBy(id = "android:id/button2")
    private static WebElement NOT_NOW_BTN;

    @iOSXCUITFindBy(accessibility = "Continue")
    private static WebElement CONTINUE_BTN;

    @iOSXCUITFindBy(accessibility = "Cancel")
    private static WebElement CANCEL_BTN;

    @AndroidFindBy(id = "com.android.chrome:id/terms_accept")
    private static WebElement ACCEPT_AND_CONTINUE;

    @AndroidFindBy(id = "android:id/button2")
    private static WebElement NO_UPDATE;

    @AndroidFindBy(id = "com.android.chrome:id/negative_button")
    private static WebElement NEGATIVE_BTN;

    public LandingPage verifyEurodataTextMessage() {
        verifyLocalizedElementText("LOGIN_TEXT", LOGIN_TEXT);
        return this;
    }

    public LoginPage login() {
        click(LOGIN_BTN);
        switchToView("WEBVIEW_");
        return new LoginPage();
    }

    public LandingPage loginBtnDisplayed() {
        assert isDisplayed(LOGIN_BTN);
        return this;
    }

    public LandingPage verifyLoginBtnText() {
        verifyLocalizedElementText("LOGIN_BTN", LOGIN_BTN);
        return this;
    }

    public LandingPage forgotPassword() {
        click(FORGOT_PASSWORD);
        return this;
    }

    public LandingPage forgotBtnDisplayed() {
        assert isDisplayed(FORGOT_PASSWORD);
        return this;
    }

    public LandingPage verifyForgotPasswordLinkText() {
        verifyLocalizedElementText("FORGOT_PASSWORD", FORGOT_PASSWORD);
        return this;
    }

    public LandingPage notNowBtn() {
        click(NOT_NOW_BTN);
        return this;
    }

    public LandingPage continueBTN() {
        click(CONTINUE_BTN);
        return this;
    }

    public LandingPage cancelBTN() {
        click(CANCEL_BTN);
        return this;
    }

    public LandingPage noUpdate() {
        click(NO_UPDATE);
        return this;
    }

    public LandingPage acceptAndContinue() {
        click(ACCEPT_AND_CONTINUE);
        return this;
    }

    public LandingPage noThanks() {
        click(NEGATIVE_BTN);
        return this;
    }
}
