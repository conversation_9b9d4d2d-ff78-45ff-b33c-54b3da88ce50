package pages.webPages;

import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;
import pages.navigationPages.DashboardPage;
import util.Base;

public class StaticPages extends Base {

    @AndroidFindBy(id = "com.android.chrome:id/url_bar")
    private static WebElement URL_BAR;

    public StaticPages verifyURL(String expectedText) {
        verifyLocalizedElementText(expectedText, URL_BAR);
        return this;
    }

    public DashboardPage backToDashboard() {
        back();
        return new DashboardPage();
    }
}
