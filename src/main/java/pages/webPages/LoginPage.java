package pages.webPages;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import pages.navigationPages.DashboardPage;
import util.Base;

public class LoginPage extends Base {

    @FindBy(id = "username")
    private static WebElement USERNAME;

    @FindBy(id = "password")
    private static WebElement PASSWORD;

    @FindBy(id = "kc-login")
    private static WebElement SIGN_IN;

    @FindBy(id = "input-error")
    private static WebElement INVALID_CREDENTIALS_ERROR_MESSAGE;

    public LoginPage setUsername(String username) {
        clear(USERNAME);
        type(USERNA<PERSON>, username);
        return this;
    }

    public LoginPage setPassword(String password) {
        clear(PASSWORD);
        type(PASSWORD, password);
        return this;
    }

    public DashboardPage signIn() {
        clickWithJavaScriptExecutor(SIGN_IN);
        switchToView("NATIVE_APP");
        return new DashboardPage();
    }

    public LoginPage signInAndStay() {
        clickWithJavaScriptExecutor(SIGN_IN);
        return this;
    }

    public LoginPage verifyInvalidCredentialsErrorMessage() {
        verifyLocalizedElementText("INVALID_CREDENTIALS_ERROR_MESSAGE", INVALID_CREDENTIALS_ERROR_MESSAGE);
        return this;
    }
}
